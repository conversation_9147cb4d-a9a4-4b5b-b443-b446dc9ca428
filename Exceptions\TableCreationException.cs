namespace WebAPIGraphQL.Exceptions;

public class TableCreationException : Exception
{
    public TableCreationException(string message) : base(message)
    {
    }

    public TableCreationException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

public class InvalidTableNameException : TableCreationException
{
    public InvalidTableNameException(string tableName) 
        : base($"Invalid table name '{tableName}'. Table name must start with a letter and contain only letters, numbers, and underscores.")
    {
    }
}

public class TableAlreadyExistsException : TableCreationException
{
    public TableAlreadyExistsException(string tableName) 
        : base($"Table '{tableName}' already exists.")
    {
    }
}

public class UnsupportedDataTypeException : TableCreationException
{
    public UnsupportedDataTypeException(string dataType) 
        : base($"Unsupported data type '{dataType}'. Supported types are: string, int, long, datetime, bool, decimal, double, guid.")
    {
    }
}
