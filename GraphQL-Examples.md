# GraphQL API Examples

This document provides example GraphQL queries and mutations for the Dynamic Table Creation API.

## GraphQL Endpoint
- **URL**: `https://localhost:7xxx/graphql` (replace xxx with actual port)
- **GraphQL Playground**: Available at the same URL in development mode

## Supported Data Types
- `string` - Text data (with optional maxLength)
- `int` - 32-bit integer
- `long` - 64-bit integer  
- `datetime` - Date and time
- `bool` - <PERSON><PERSON>an (true/false)
- `decimal` - Decimal number with precision
- `double` - Double precision floating point
- `guid` - Globally unique identifier

## Example Mutations

### 1. Create a Simple User Table
```graphql
mutation {
  createTable(request: {
    tableName: "Users"
    customColumns: [
      {
        name: "FirstName"
        dataType: "string"
        maxLength: 50
        isNullable: false
      }
      {
        name: "LastName"
        dataType: "string"
        maxLength: 50
        isNullable: false
      }
      {
        name: "Email"
        dataType: "string"
        maxLength: 255
        isNullable: false
        isUnique: true
      }
      {
        name: "Age"
        dataType: "int"
        isNullable: true
      }
      {
        name: "IsActive"
        dataType: "bool"
        isNullable: false
        defaultValue: "true"
      }
    ]
  }) {
    success
    message
    tableName
    createdColumns
  }
}
```

### 2. Create a Product Table with Various Data Types
```graphql
mutation {
  createTable(request: {
    tableName: "Products"
    customColumns: [
      {
        name: "ProductName"
        dataType: "string"
        maxLength: 100
        isNullable: false
      }
      {
        name: "Price"
        dataType: "decimal"
        isNullable: false
      }
      {
        name: "Weight"
        dataType: "double"
        isNullable: true
      }
      {
        name: "ProductId"
        dataType: "guid"
        isNullable: false
        isUnique: true
        defaultValue: "newid"
      }
      {
        name: "LaunchDate"
        dataType: "datetime"
        isNullable: true
      }
      {
        name: "InStock"
        dataType: "bool"
        isNullable: false
        defaultValue: "false"
      }
    ]
  }) {
    success
    message
    tableName
    createdColumns
  }
}
```

### 3. Create a Simple Log Table
```graphql
mutation {
  createTable(request: {
    tableName: "ApplicationLogs"
    customColumns: [
      {
        name: "LogLevel"
        dataType: "string"
        maxLength: 20
        isNullable: false
        defaultValue: "Info"
      }
      {
        name: "Message"
        dataType: "string"
        isNullable: false
      }
      {
        name: "UserId"
        dataType: "long"
        isNullable: true
      }
    ]
  }) {
    success
    message
    tableName
    createdColumns
  }
}
```

## Example Queries

### 1. Check if a Table Exists
```graphql
query {
  tableExists(tableName: "Users")
}
```

### 2. Get Table Columns
```graphql
query {
  getTableColumns(tableName: "Users")
}
```

### 3. Get Supported Data Types
```graphql
query {
  getSupportedDataTypes
}
```

## Default Columns
Every table created through this API automatically includes these default columns:
- `Id` (INT IDENTITY PRIMARY KEY) - Auto-incrementing unique identifier
- `CreatedDate` (DATETIME2) - Timestamp when record was created (defaults to GETUTCDATE())
- `UpdatedDate` (DATETIME2) - Timestamp when record was last updated (defaults to GETUTCDATE())

## Error Handling
The API includes comprehensive error handling:
- Invalid table names (must start with letter, contain only letters/numbers/underscores)
- Duplicate table names
- Unsupported data types
- SQL execution errors

## Response Format
All mutations return a standardized response:
```graphql
{
  success: Boolean!
  message: String!
  tableName: String
  createdColumns: [String!]!
}
```

## Special Default Values
- For `datetime` fields: Use `"now"` to set default to current UTC time
- For `bool` fields: Use `"true"` or `"false"`
- For `guid` fields: Use `"newid"` to generate new GUID
- For other types: Provide the literal value as string
