using WebAPIGraphQL.GraphQL.Types;
using WebAPIGraphQL.Models;
using WebAPIGraphQL.Services;

namespace WebAPIGraphQL.GraphQL.Mutations;

public class TableMutation
{
    public async Task<CreateTableResponseType> CreateTableAsync(
        CreateTableRequestType request,
        [Service] IDynamicTableService tableService)
    {
        // Map GraphQL types to domain models
        var domainRequest = new CreateTableRequest
        {
            TableName = request.TableName,
            CustomColumns = request.CustomColumns.Select(c => new ColumnDefinition
            {
                Name = c.Name,
                DataType = c.DataType,
                IsNullable = c.IsNullable,
                MaxLength = c.<PERSON>,
                IsUnique = c.IsUnique,
                DefaultValue = c.<PERSON>al<PERSON>
            }).ToList()
        };

        var result = await tableService.CreateTableAsync(domainRequest);

        // Map domain result to GraphQL type
        return new CreateTableResponseType
        {
            Success = result.Success,
            Message = result.Message,
            TableName = result.TableName,
            CreatedColumns = result.CreatedColumns
        };
    }

    public async Task<bool> TableExistsAsync(
        string tableName,
        [Service] IDynamicTableService tableService)
    {
        return await tableService.TableExistsAsync(tableName);
    }

    public async Task<List<string>> GetTableColumnsAsync(
        string tableName,
        [Service] IDynamicTableService tableService)
    {
        return await tableService.GetTableColumnsAsync(tableName);
    }
}
