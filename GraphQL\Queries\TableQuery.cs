using WebAPIGraphQL.Services;

namespace WebAPIGraphQL.GraphQL.Queries;

public class TableQuery
{
    public async Task<bool> TableExistsAsync(
        string tableName,
        [Service] IDynamicTableService tableService)
    {
        return await tableService.TableExistsAsync(tableName);
    }

    public async Task<List<string>> GetTableColumnsAsync(
        string tableName,
        [Service] IDynamicTableService tableService)
    {
        return await tableService.GetTableColumnsAsync(tableName);
    }

    public string GetSupportedDataTypes()
    {
        return "Supported data types: string, int, long, datetime, bool, decimal, double, guid";
    }
}
