using WebAPIGraphQL.Models;

namespace WebAPIGraphQL.GraphQL.Types;

public class ColumnDefinitionType
{
    public string Name { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public bool IsNullable { get; set; } = true;
    public int? MaxLength { get; set; }
    public bool IsUnique { get; set; } = false;
    public string? DefaultValue { get; set; }
}

public class CreateTableRequestType
{
    public string TableName { get; set; } = string.Empty;
    public List<ColumnDefinitionType> CustomColumns { get; set; } = new();
}

public class CreateTableResponseType
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? TableName { get; set; }
    public List<string> CreatedColumns { get; set; } = new();
}
