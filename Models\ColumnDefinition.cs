using System.ComponentModel.DataAnnotations;

namespace WebAPIGraphQL.Models;

public class ColumnDefinition
{
    [Required]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public string DataType { get; set; } = string.Empty;
    
    public bool IsNullable { get; set; } = true;
    
    public int? MaxLength { get; set; }
    
    public bool IsUnique { get; set; } = false;
    
    public string? DefaultValue { get; set; }
}

public class CreateTableRequest
{
    [Required]
    public string TableName { get; set; } = string.Empty;
    
    public List<ColumnDefinition> CustomColumns { get; set; } = new();
}

public class CreateTableResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? TableName { get; set; }
    public List<string> CreatedColumns { get; set; } = new();
}

public enum SupportedDataType
{
    String,
    Int,
    Long,
    DateTime,
    Bool,
    Decimal,
    Double,
    Guid
}
