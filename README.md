# GraphQL Web API for Dynamic Table Creation

A modern .NET 8 GraphQL Web API that enables dynamic database table creation with custom columns using Entity Framework Core and SQL Server LocalDB.

## Features

- **GraphQL API**: Full GraphQL implementation with queries and mutations (not REST)
- **Dynamic Table Creation**: Create database tables with custom columns at runtime
- **Multiple Data Types**: Support for string, int, long, datetime, bool, decimal, double, guid
- **Default Columns**: Automatic Id, CreatedDate, and UpdatedDate columns
- **Advanced Column Features**: Nullable/non-nullable, unique constraints, default values, max length
- **Comprehensive Validation**: Table names, column names, data types, and constraints
- **Error Handling**: Graceful error responses with meaningful messages
- **Documentation**: Swagger/OpenAPI integration and GraphQL Playground
- **Modern Architecture**: .NET 8, HotChocolate GraphQL, Entity Framework Core

## Quick Start

### Prerequisites
- .NET 8 SDK
- SQL Server LocalDB

### Running the Application
```bash
dotnet run
```

The application will start on `http://localhost:5114`

### Access Points
- **GraphQL Endpoint**: http://localhost:5114/graphql
- **GraphQL Playground**: http://localhost:5114/graphql (interactive)
- **Swagger Documentation**: http://localhost:5114/swagger
- **Health Check**: http://localhost:5114/health

## Usage Examples

### Create a User Table
```graphql
mutation {
  createTable(request: {
    tableName: "Users"
    customColumns: [
      {
        name: "FirstName"
        dataType: "string"
        maxLength: 50
        isNullable: false
      }
      {
        name: "Email"
        dataType: "string"
        maxLength: 255
        isNullable: false
        isUnique: true
      }
      {
        name: "Age"
        dataType: "int"
        isNullable: true
      }
      {
        name: "IsActive"
        dataType: "bool"
        isNullable: false
        defaultValue: "true"
      }
    ]
  }) {
    success
    message
    tableName
    createdColumns
  }
}
```

### Query Table Information
```graphql
query {
  # Check if table exists
  tableExists(tableName: "Users")
  
  # Get table columns
  tableColumns(tableName: "Users")
  
  # Get supported data types
  supportedDataTypes
}
```

## Database Configuration

The application uses SQL Server LocalDB with the following connection string:
```
Data Source=(localdb)\MSSQLLocalDB;Initial Catalog=Infodat;Integrated Security=True;Persist Security Info=False;Pooling=False;Multiple Active Result Sets=False;Encrypt=True;Trust Server Certificate=False;Command Timeout=0
```

## Project Structure

```
WebAPIGraphQL/
├── Data/
│   └── ApplicationDbContext.cs
├── Exceptions/
│   └── TableCreationException.cs
├── GraphQL/
│   ├── Mutations/
│   │   └── TableMutation.cs
│   ├── Queries/
│   │   └── TableQuery.cs
│   └── Types/
│       └── ColumnDefinitionType.cs
├── Models/
│   └── ColumnDefinition.cs
├── Services/
│   ├── IDynamicTableService.cs
│   └── DynamicTableService.cs
├── Validators/
│   └── TableRequestValidator.cs
├── Program.cs
├── GraphQL-Examples.md
├── TEST-RESULTS.md
└── README.md
```

## Supported Data Types

- **string**: Text data with optional maxLength (up to 8000 characters)
- **int**: 32-bit signed integer
- **long**: 64-bit signed integer
- **datetime**: Date and time (DATETIME2)
- **bool**: Boolean true/false (BIT)
- **decimal**: Decimal number with precision (DECIMAL(18,2))
- **double**: Double precision floating point (FLOAT)
- **guid**: Globally unique identifier (UNIQUEIDENTIFIER)

## Default Columns

Every table automatically includes:
- **Id**: INT IDENTITY(1,1) PRIMARY KEY
- **CreatedDate**: DATETIME2 NOT NULL DEFAULT GETUTCDATE()
- **UpdatedDate**: DATETIME2 NOT NULL DEFAULT GETUTCDATE()

## Validation Rules

### Table Names
- Must start with a letter
- Can contain only letters, numbers, and underscores
- Maximum 128 characters
- Cannot be reserved SQL keywords

### Column Names
- Must start with a letter
- Can contain only letters, numbers, and underscores
- Maximum 128 characters
- Cannot be reserved SQL keywords
- Must be unique within the table

### Data Types
- Must be one of the supported types
- String columns can have optional maxLength
- Default values must match the data type

## Error Handling

The API provides comprehensive error handling with:
- Input validation errors
- Database constraint violations
- SQL execution errors
- Meaningful error messages
- Structured logging

## Testing

See `TEST-RESULTS.md` for comprehensive test results and examples.

## Development

### Building
```bash
dotnet build
```

### Running Tests
```bash
dotnet test
```

### Adding New Data Types
1. Update `SupportedDataType` enum in `Models/ColumnDefinition.cs`
2. Add mapping in `DynamicTableService.GetSqlDataType()`
3. Update validation in `TableRequestValidator`

## License

This project is licensed under the MIT License.
