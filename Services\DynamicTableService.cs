using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Text;
using WebAPIGraphQL.Data;
using WebAPIGraphQL.Models;
using WebAPIGraphQL.Exceptions;
using WebAPIGraphQL.Validators;

namespace WebAPIGraphQL.Services;

public class DynamicTableService : IDynamicTableService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<DynamicTableService> _logger;

    public DynamicTableService(ApplicationDbContext context, ILogger<DynamicTableService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<CreateTableResponse> CreateTableAsync(CreateTableRequest request)
    {
        try
        {
            // Validate the entire request
            TableRequestValidator.ValidateCreateTableRequest(request);

            // Check if table already exists
            if (await TableExistsAsync(request.TableName))
            {
                throw new TableAlreadyExistsException(request.TableName);
            }

            // Build SQL command
            var sql = BuildCreateTableSql(request.TableName, request.CustomColumns);
            
            _logger.LogInformation("Creating table with SQL: {Sql}", sql);

            // Execute the SQL command
            await _context.Database.ExecuteSqlRawAsync(sql);

            var createdColumns = new List<string> { "Id", "CreatedDate", "UpdatedDate" };
            createdColumns.AddRange(request.CustomColumns.Select(c => c.Name));

            return new CreateTableResponse
            {
                Success = true,
                Message = $"Table '{request.TableName}' created successfully.",
                TableName = request.TableName,
                CreatedColumns = createdColumns
            };
        }
        catch (TableCreationException ex)
        {
            _logger.LogWarning(ex, "Table creation validation failed for {TableName}", request.TableName);
            return new CreateTableResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument for table creation {TableName}", request.TableName);
            return new CreateTableResponse
            {
                Success = false,
                Message = ex.Message
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error creating table {TableName}", request.TableName);
            return new CreateTableResponse
            {
                Success = false,
                Message = $"An unexpected error occurred while creating the table: {ex.Message}"
            };
        }
    }

    public async Task<bool> TableExistsAsync(string tableName)
    {
        try
        {
            var sql = @"
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = {0} AND TABLE_SCHEMA = 'dbo'";
            
            var count = await _context.Database.SqlQueryRaw<int>(sql, tableName).FirstOrDefaultAsync();
            return count > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if table {TableName} exists", tableName);
            return false;
        }
    }

    public async Task<List<string>> GetTableColumnsAsync(string tableName)
    {
        try
        {
            var sql = @"
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = {0} AND TABLE_SCHEMA = 'dbo'
                ORDER BY ORDINAL_POSITION";
            
            var columns = await _context.Database.SqlQueryRaw<string>(sql, tableName).ToListAsync();
            return columns;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting columns for table {TableName}", tableName);
            return new List<string>();
        }
    }

    private string BuildCreateTableSql(string tableName, List<ColumnDefinition> customColumns)
    {
        var sql = new StringBuilder();
        sql.AppendLine($"CREATE TABLE [{tableName}] (");
        
        // Add default columns
        sql.AppendLine("    [Id] INT IDENTITY(1,1) PRIMARY KEY,");
        sql.AppendLine("    [CreatedDate] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),");
        sql.AppendLine("    [UpdatedDate] DATETIME2 NOT NULL DEFAULT GETUTCDATE()");
        
        // Add custom columns
        foreach (var column in customColumns)
        {
            sql.AppendLine(",");
            sql.Append($"    [{column.Name}] {GetSqlDataType(column)}");
            
            if (!column.IsNullable)
                sql.Append(" NOT NULL");
            
            if (!string.IsNullOrEmpty(column.DefaultValue))
                sql.Append($" DEFAULT {FormatDefaultValue(column.DefaultValue, column.DataType)}");
        }
        
        sql.AppendLine();
        sql.AppendLine(");");
        
        // Add unique constraints if any
        foreach (var column in customColumns.Where(c => c.IsUnique))
        {
            sql.AppendLine($"CREATE UNIQUE INDEX IX_{tableName}_{column.Name} ON [{tableName}] ([{column.Name}]);");
        }
        
        return sql.ToString();
    }

    private string GetSqlDataType(ColumnDefinition column)
    {
        return column.DataType.ToLower() switch
        {
            "string" => column.MaxLength.HasValue ? $"NVARCHAR({column.MaxLength})" : "NVARCHAR(MAX)",
            "int" => "INT",
            "long" => "BIGINT",
            "datetime" => "DATETIME2",
            "bool" => "BIT",
            "decimal" => "DECIMAL(18,2)",
            "double" => "FLOAT",
            "guid" => "UNIQUEIDENTIFIER",
            _ => throw new ArgumentException($"Unsupported data type: {column.DataType}")
        };
    }

    private string FormatDefaultValue(string defaultValue, string dataType)
    {
        return dataType.ToLower() switch
        {
            "string" => $"'{defaultValue}'",
            "datetime" when defaultValue.ToLower() == "now" => "GETUTCDATE()",
            "bool" when defaultValue.ToLower() == "true" => "1",
            "bool" when defaultValue.ToLower() == "false" => "0",
            "guid" when defaultValue.ToLower() == "newid" => "NEWID()",
            _ => defaultValue
        };
    }


}
