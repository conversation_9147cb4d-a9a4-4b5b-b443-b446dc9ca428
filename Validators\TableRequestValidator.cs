using WebAPIGraphQL.Models;
using WebAPIGraphQL.Exceptions;

namespace WebAPIGraphQL.Validators;

public static class TableRequestValidator
{
    private static readonly HashSet<string> SupportedDataTypes = new(StringComparer.OrdinalIgnoreCase)
    {
        "string", "int", "long", "datetime", "bool", "decimal", "double", "guid"
    };

    private static readonly HashSet<string> ReservedWords = new(StringComparer.OrdinalIgnoreCase)
    {
        "id", "createddate", "updateddate", "select", "insert", "update", "delete", 
        "from", "where", "order", "group", "having", "join", "union", "table", 
        "database", "schema", "index", "primary", "foreign", "key", "constraint"
    };

    public static void ValidateCreateTableRequest(CreateTableRequest request)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        ValidateTableName(request.TableName);
        ValidateCustomColumns(request.CustomColumns);
    }

    public static void ValidateTableName(string tableName)
    {
        if (string.IsNullOrWhiteSpace(tableName))
            throw new InvalidTableNameException("Table name cannot be empty");

        if (tableName.Length > 128)
            throw new InvalidTableNameException("Table name cannot exceed 128 characters");

        if (!char.IsLetter(tableName[0]))
            throw new InvalidTableNameException("Table name must start with a letter");

        if (!tableName.All(c => char.IsLetterOrDigit(c) || c == '_'))
            throw new InvalidTableNameException("Table name can only contain letters, numbers, and underscores");

        if (ReservedWords.Contains(tableName))
            throw new InvalidTableNameException($"'{tableName}' is a reserved word and cannot be used as a table name");
    }

    public static void ValidateCustomColumns(List<ColumnDefinition> columns)
    {
        if (columns == null)
            return;

        var columnNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        foreach (var column in columns)
        {
            ValidateColumnDefinition(column);

            // Check for duplicate column names
            if (!columnNames.Add(column.Name))
                throw new ArgumentException($"Duplicate column name '{column.Name}'");

            // Check for reserved column names
            if (ReservedWords.Contains(column.Name))
                throw new ArgumentException($"'{column.Name}' is a reserved word and cannot be used as a column name");
        }
    }

    public static void ValidateColumnDefinition(ColumnDefinition column)
    {
        if (column == null)
            throw new ArgumentNullException(nameof(column));

        if (string.IsNullOrWhiteSpace(column.Name))
            throw new ArgumentException("Column name cannot be empty");

        if (column.Name.Length > 128)
            throw new ArgumentException($"Column name '{column.Name}' cannot exceed 128 characters");

        if (!char.IsLetter(column.Name[0]))
            throw new ArgumentException($"Column name '{column.Name}' must start with a letter");

        if (!column.Name.All(c => char.IsLetterOrDigit(c) || c == '_'))
            throw new ArgumentException($"Column name '{column.Name}' can only contain letters, numbers, and underscores");

        if (string.IsNullOrWhiteSpace(column.DataType))
            throw new ArgumentException($"Data type for column '{column.Name}' cannot be empty");

        if (!SupportedDataTypes.Contains(column.DataType))
            throw new UnsupportedDataTypeException(column.DataType);

        // Validate MaxLength for string types
        if (column.DataType.Equals("string", StringComparison.OrdinalIgnoreCase))
        {
            if (column.MaxLength.HasValue && column.MaxLength.Value <= 0)
                throw new ArgumentException($"MaxLength for column '{column.Name}' must be greater than 0");

            if (column.MaxLength.HasValue && column.MaxLength.Value > 8000)
                throw new ArgumentException($"MaxLength for column '{column.Name}' cannot exceed 8000 characters");
        }
        else if (column.MaxLength.HasValue)
        {
            throw new ArgumentException($"MaxLength can only be specified for string columns, not for '{column.DataType}' column '{column.Name}'");
        }

        // Validate default values
        if (!string.IsNullOrEmpty(column.DefaultValue))
        {
            ValidateDefaultValue(column.Name, column.DataType, column.DefaultValue);
        }
    }

    private static void ValidateDefaultValue(string columnName, string dataType, string defaultValue)
    {
        switch (dataType.ToLower())
        {
            case "int":
                if (!int.TryParse(defaultValue, out _))
                    throw new ArgumentException($"Invalid default value '{defaultValue}' for int column '{columnName}'");
                break;

            case "long":
                if (!long.TryParse(defaultValue, out _))
                    throw new ArgumentException($"Invalid default value '{defaultValue}' for long column '{columnName}'");
                break;

            case "decimal":
                if (!decimal.TryParse(defaultValue, out _))
                    throw new ArgumentException($"Invalid default value '{defaultValue}' for decimal column '{columnName}'");
                break;

            case "double":
                if (!double.TryParse(defaultValue, out _))
                    throw new ArgumentException($"Invalid default value '{defaultValue}' for double column '{columnName}'");
                break;

            case "bool":
                if (!defaultValue.Equals("true", StringComparison.OrdinalIgnoreCase) && 
                    !defaultValue.Equals("false", StringComparison.OrdinalIgnoreCase))
                    throw new ArgumentException($"Invalid default value '{defaultValue}' for bool column '{columnName}'. Use 'true' or 'false'");
                break;

            case "datetime":
                if (!defaultValue.Equals("now", StringComparison.OrdinalIgnoreCase) && 
                    !DateTime.TryParse(defaultValue, out _))
                    throw new ArgumentException($"Invalid default value '{defaultValue}' for datetime column '{columnName}'. Use 'now' or a valid date string");
                break;

            case "guid":
                if (!defaultValue.Equals("newid", StringComparison.OrdinalIgnoreCase) && 
                    !Guid.TryParse(defaultValue, out _))
                    throw new ArgumentException($"Invalid default value '{defaultValue}' for guid column '{columnName}'. Use 'newid' or a valid GUID string");
                break;
        }
    }
}
