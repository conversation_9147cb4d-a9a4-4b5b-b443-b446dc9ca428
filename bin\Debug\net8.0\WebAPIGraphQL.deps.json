{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"WebAPIGraphQL/1.0.0": {"dependencies": {"HotChocolate.AspNetCore": "15.1.10", "Microsoft.AspNetCore.OpenApi": "8.0.19", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.9", "Microsoft.EntityFrameworkCore.Tools": "9.0.9", "Swashbuckle.AspNetCore": "6.6.2"}, "runtime": {"WebAPIGraphQL.dll": {}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "9.0.9", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "9.0.9", "System.Text.Json": "9.0.9", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "9.0.9", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "ChilliCream.Nitro.App/28.0.7": {"dependencies": {"Yarp.ReverseProxy": "2.3.0"}, "runtime": {"lib/net8.0/ChilliCream.Nitro.App.dll": {"assemblyVersion": "2*******", "fileVersion": "28.0.7.0"}}}, "GreenDonut/15.1.10": {"dependencies": {"GreenDonut.Abstractions": "15.1.10", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.ObjectPool": "8.0.0"}, "runtime": {"lib/net8.0/GreenDonut.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "GreenDonut.Abstractions/15.1.10": {"runtime": {"lib/net8.0/GreenDonut.Abstractions.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "GreenDonut.Data/15.1.10": {"dependencies": {"GreenDonut": "15.1.10", "GreenDonut.Data.Abstractions": "15.1.10"}, "runtime": {"lib/net8.0/GreenDonut.Data.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "GreenDonut.Data.Abstractions/15.1.10": {"dependencies": {"GreenDonut.Abstractions": "15.1.10", "GreenDonut.Data.Primitives": "15.1.10"}, "runtime": {"lib/net8.0/GreenDonut.Data.Abstractions.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "GreenDonut.Data.Primitives/15.1.10": {"runtime": {"lib/net8.0/GreenDonut.Data.Primitives.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate/15.1.10": {"dependencies": {"HotChocolate.Authorization": "15.1.10", "HotChocolate.CostAnalysis": "15.1.10", "HotChocolate.Execution": "15.1.10", "HotChocolate.Execution.Projections": "15.1.10", "HotChocolate.Fetching": "15.1.10", "HotChocolate.Types": "15.1.10", "HotChocolate.Types.CursorPagination": "15.1.10", "HotChocolate.Types.CursorPagination.Extensions": "15.1.10", "HotChocolate.Types.Mutations": "15.1.10", "HotChocolate.Types.Queries": "15.1.10", "HotChocolate.Validation": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Abstractions/15.1.10": {"dependencies": {"HotChocolate.Primitives": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Abstractions.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.AspNetCore/15.1.10": {"dependencies": {"ChilliCream.Nitro.App": "28.0.7", "HotChocolate": "15.1.10", "HotChocolate.Subscriptions.InMemory": "15.1.10", "HotChocolate.Transport.Sockets": "15.1.10", "HotChocolate.Types.Scalars.Upload": "15.1.10", "HotChocolate.Utilities.DependencyInjection": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.AspNetCore.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Authorization/15.1.10": {"dependencies": {"HotChocolate.Execution": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Authorization.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.CostAnalysis/15.1.10": {"dependencies": {"HotChocolate.Execution": "15.1.10", "HotChocolate.Types": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.CostAnalysis.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Execution/15.1.10": {"dependencies": {"HotChocolate.Abstractions": "15.1.10", "HotChocolate.Execution.Abstractions": "15.1.10", "HotChocolate.Fetching": "15.1.10", "HotChocolate.Types": "15.1.10", "HotChocolate.Utilities.DependencyInjection": "15.1.10", "HotChocolate.Validation": "15.1.10", "Microsoft.Extensions.DependencyInjection": "9.0.9", "System.IO.Pipelines": "9.0.9"}, "runtime": {"lib/net8.0/HotChocolate.Execution.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Execution.Abstractions/15.1.10": {"dependencies": {"HotChocolate.Abstractions": "15.1.10", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9"}, "runtime": {"lib/net8.0/HotChocolate.Execution.Abstractions.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Execution.Projections/15.1.10": {"dependencies": {"GreenDonut.Data": "15.1.10", "GreenDonut.Data.Abstractions": "15.1.10", "GreenDonut.Data.Primitives": "15.1.10", "HotChocolate.Execution": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Execution.Projections.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Features/15.1.10": {"runtime": {"lib/net8.0/HotChocolate.Features.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Fetching/15.1.10": {"dependencies": {"GreenDonut": "15.1.10", "HotChocolate.Types": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Fetching.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Language/15.1.10": {"dependencies": {"HotChocolate.Language.SyntaxTree": "15.1.10", "HotChocolate.Language.Utf8": "15.1.10", "HotChocolate.Language.Visitors": "15.1.10", "HotChocolate.Language.Web": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Language.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Language.SyntaxTree/15.1.10": {"dependencies": {"Microsoft.Extensions.ObjectPool": "8.0.0"}, "runtime": {"lib/net8.0/HotChocolate.Language.SyntaxTree.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Language.Utf8/15.1.10": {"dependencies": {"HotChocolate.Language.SyntaxTree": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Language.Utf8.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Language.Visitors/15.1.10": {"dependencies": {"HotChocolate.Language.SyntaxTree": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Language.Visitors.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Language.Web/15.1.10": {"dependencies": {"HotChocolate.Language.Utf8": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Language.Web.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Primitives/15.1.10": {"dependencies": {"HotChocolate.Language": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Primitives.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Subscriptions/15.1.10": {"dependencies": {"HotChocolate.Abstractions": "15.1.10", "HotChocolate.Execution.Abstractions": "15.1.10", "HotChocolate.Utilities": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Subscriptions.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Subscriptions.InMemory/15.1.10": {"dependencies": {"HotChocolate.Execution.Abstractions": "15.1.10", "HotChocolate.Subscriptions": "15.1.10", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9"}, "runtime": {"lib/net8.0/HotChocolate.Subscriptions.InMemory.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Transport.Sockets/15.1.10": {"dependencies": {"System.IO.Pipelines": "9.0.9"}, "runtime": {"lib/net8.0/HotChocolate.Transport.Sockets.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Types/15.1.10": {"dependencies": {"GreenDonut": "15.1.10", "HotChocolate.Abstractions": "15.1.10", "HotChocolate.Features": "15.1.10", "HotChocolate.Types.Shared": "15.1.10", "HotChocolate.Utilities": "15.1.10", "Microsoft.Extensions.DependencyInjection": "9.0.9", "Microsoft.Extensions.ObjectPool": "8.0.0"}, "runtime": {"lib/net8.0/HotChocolate.Types.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Types.CursorPagination/15.1.10": {"dependencies": {"HotChocolate.Execution.Abstractions": "15.1.10", "HotChocolate.Types": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Types.CursorPagination.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Types.CursorPagination.Extensions/15.1.10": {"dependencies": {"GreenDonut.Data": "15.1.10", "HotChocolate.Types.CursorPagination": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Types.CursorPagination.Extensions.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Types.Errors/15.1.10": {"dependencies": {"HotChocolate.Execution": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Types.Errors.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Types.Mutations/15.1.10": {"dependencies": {"HotChocolate.Execution": "15.1.10", "HotChocolate.Types.Errors": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Types.Mutations.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Types.Queries/15.1.10": {"dependencies": {"HotChocolate.Execution": "15.1.10", "HotChocolate.Types.Errors": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Types.Queries.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Types.Scalars.Upload/15.1.10": {"dependencies": {"HotChocolate.Types": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Types.Scalars.Upload.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Types.Shared/15.1.10": {"dependencies": {"HotChocolate.Language.SyntaxTree": "15.1.10"}, "runtime": {"lib/net8.0/HotChocolate.Types.Shared.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Utilities/15.1.10": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9"}, "runtime": {"lib/net8.0/HotChocolate.Utilities.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Utilities.DependencyInjection/15.1.10": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.9"}, "runtime": {"lib/net8.0/HotChocolate.Utilities.DependencyInjection.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "HotChocolate.Validation/15.1.10": {"dependencies": {"HotChocolate.Types": "15.1.10", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9"}, "runtime": {"lib/net8.0/HotChocolate.Validation.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.AspNetCore.OpenApi/8.0.19": {"dependencies": {"Microsoft.OpenApi": "1.6.14"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "8.0.19.0", "fileVersion": "8.0.1925.37204"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Build.Framework/17.8.3": {}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "*******", "fileVersion": "1.7.8.28074"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "9.0.9", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "System.Text.Json": "9.0.9"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/5.1.6": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.1", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "9.0.9", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "9.0.9"}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.16.24240.5"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/9.0.9": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.9", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41909"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.9": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41909"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.9": {}, "Microsoft.EntityFrameworkCore.Design/9.0.9": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyModel": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41909"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.9": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41909"}}}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.9": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.6", "Microsoft.EntityFrameworkCore.Relational": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "System.Formats.Asn1": "9.0.9", "System.Text.Json": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41909"}}}, "Microsoft.EntityFrameworkCore.Tools/9.0.9": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "9.0.9"}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.9": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Caching.Memory/9.0.9": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.9": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.DependencyInjection/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.9": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.DependencyModel/9.0.9": {"dependencies": {"System.Text.Encodings.Web": "9.0.9", "System.Text.Json": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "9.0.0.9", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Logging/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "System.Diagnostics.DiagnosticSource": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.ObjectPool/8.0.0": {}, "Microsoft.Extensions.Options/9.0.9": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Extensions.Primitives/9.0.9": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "9.0.9"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "9.0.9", "System.Text.Json": "9.0.9"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.6.14": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.14.0", "fileVersion": "1.6.14.0"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/6.6.2": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.6.2", "Swashbuckle.AspNetCore.SwaggerGen": "6.6.2", "Swashbuckle.AspNetCore.SwaggerUI": "6.6.2"}}, "Swashbuckle.AspNetCore.Swagger/6.6.2": {"dependencies": {"Microsoft.OpenApi": "1.6.14"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "6.6.2.401"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.6.2": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.6.2"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "6.6.2.401"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.6.2": {"runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.6.2.401"}}}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "9.0.9"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Collections.Immutable/7.0.0": {}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}}, "System.Diagnostics.DiagnosticSource/9.0.9": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Formats.Asn1/9.0.9": {"runtime": {"lib/net8.0/System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "6.35.0.41201"}}}, "System.IO.Hashing/8.0.0": {"runtime": {"lib/net8.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.IO.Pipelines/9.0.9": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "9.0.9", "System.Text.Json": "9.0.9"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection.Metadata/7.0.0": {"dependencies": {"System.Collections.Immutable": "7.0.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "9.0.9"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/9.0.9": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.Text.Json/9.0.9": {"dependencies": {"System.IO.Pipelines": "9.0.9", "System.Text.Encodings.Web": "9.0.9"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.925.41916"}}}, "System.Threading.Channels/7.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Yarp.ReverseProxy/2.3.0": {"dependencies": {"System.IO.Hashing": "8.0.0"}, "runtime": {"lib/net8.0/Yarp.ReverseProxy.dll": {"assemblyVersion": "*******", "fileVersion": "2.300.25.12702"}}}}}, "libraries": {"WebAPIGraphQL/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "ChilliCream.Nitro.App/28.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON><PERSON><PERSON>od8ForeoVMXigRyAxTTqiiUYcppE85UY/zy4XLMunpF1Ginn5njWK+F+93bw3x5SssPR/fexEpOtQqkVLw==", "path": "chillicream.nitro.app/28.0.7", "hashPath": "chillicream.nitro.app.28.0.7.nupkg.sha512"}, "GreenDonut/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-gAjgkU+gw0oibYArtYUuZSYpyiUe8/FtRViAzz/b4CgGLmwXuGDVvmwo6ofWHZksvgqvj9pPll9XfIxN3VFj1Q==", "path": "greendonut/15.1.10", "hashPath": "greendonut.15.1.10.nupkg.sha512"}, "GreenDonut.Abstractions/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-VbUSwLzcdOgkXeSCCYC3AMnf0T1b9U//ZjvjjH+7FQPAVh4ofTiSpQeb0YH9ldMNblkcHxmhwH3HhwV/J3OmKA==", "path": "greendonut.abstractions/15.1.10", "hashPath": "greendonut.abstractions.15.1.10.nupkg.sha512"}, "GreenDonut.Data/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-2MwGRKsi80wOREjXB6scN/tjNVFDwjXivzdMnZRcLbzo6RVPp9ohMv7chPLSBJk0A1c/VhLEO5K5CKjsmWJ23g==", "path": "greendonut.data/15.1.10", "hashPath": "greendonut.data.15.1.10.nupkg.sha512"}, "GreenDonut.Data.Abstractions/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-99a0apQ0QYLJFG/GD+eyA5fHpjchcFPMIwLw+fZCgfo1wC72rH3fDBtsz8HOwvWhhDmfmeDnYg9ojE4icUwZnQ==", "path": "greendonut.data.abstractions/15.1.10", "hashPath": "greendonut.data.abstractions.15.1.10.nupkg.sha512"}, "GreenDonut.Data.Primitives/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-lP8eH5VB1f8lWmzHGhmUXBP7r81vtU4+GDk7YpH2tE1NtHC452EtIXdg//meFOlu3W/mMRFT4s+7cLUaaK60/g==", "path": "greendonut.data.primitives/15.1.10", "hashPath": "greendonut.data.primitives.15.1.10.nupkg.sha512"}, "HotChocolate/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-wLTht2pOEKNVlCJAvQqTnanGPaIXSaQOrrozg3Bxyk+yvboxG67NWZNxmbCE9iOHUvXAl9/Ia+1z7TP+Uhhlpg==", "path": "hotchocolate/15.1.10", "hashPath": "hotchocolate.15.1.10.nupkg.sha512"}, "HotChocolate.Abstractions/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-kMS0XHVuVs/j6Xs4XAJfn35mvfuFBeFvPC6u8QKGufyG7Pl6URdjs1QuCT/yiiZLhyDT5pU91PPNvSx2TygRyQ==", "path": "hotchocolate.abstractions/15.1.10", "hashPath": "hotchocolate.abstractions.15.1.10.nupkg.sha512"}, "HotChocolate.AspNetCore/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-IhYJND+7kbYw22NZhHjokwGvbPfEVHcoO7QUSjGDtSGMYPdODF8mNEPcdAALCOgA7OjnjkzLHfqj6I4w/W36jQ==", "path": "hotchocolate.aspnetcore/15.1.10", "hashPath": "hotchocolate.aspnetcore.15.1.10.nupkg.sha512"}, "HotChocolate.Authorization/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-SumTBGpVB4aSmODXUTtPILTygneJjcK8s9wmp9OEHPP5DdPjixCrIxIVmm92JJ/N/lwmZoGp95xfOtLu57smvg==", "path": "hotchocolate.authorization/15.1.10", "hashPath": "hotchocolate.authorization.15.1.10.nupkg.sha512"}, "HotChocolate.CostAnalysis/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-RoZWGtJcaqIykFq/iA9rTq35lEZaFb4XA/+PNdMMo1G2NCsfTxRDdn2R9x8X9kc9sZ4WA+jN1oSPFh87VOB0kg==", "path": "hotchocolate.costanalysis/15.1.10", "hashPath": "hotchocolate.costanalysis.15.1.10.nupkg.sha512"}, "HotChocolate.Execution/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-CWOZJgsZzbqA8v1BjXQHEc3oZcirUgQ9Q0s66B2qa/M44CbATO3D8D0FjF4BNBUf/LXcHQPbxG521qsBukIcSg==", "path": "hotchocolate.execution/15.1.10", "hashPath": "hotchocolate.execution.15.1.10.nupkg.sha512"}, "HotChocolate.Execution.Abstractions/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-BMlTf8Z7J6FIVpsO9jRVWpcDDbj9MPGK0MRb5CVnEOQ7TQvEoWHuJuZuiZpbOdxCmHp4frn4tD17r9YG11JBbQ==", "path": "hotchocolate.execution.abstractions/15.1.10", "hashPath": "hotchocolate.execution.abstractions.15.1.10.nupkg.sha512"}, "HotChocolate.Execution.Projections/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-hxgUJZEjF7z3IoZTvpZ+mJaFEMtXmYHo2jkEZe5WV9HsrKSh2msfyxE6lhebLOfixAcLC/Xdab9WjqHXYVGwvA==", "path": "hotchocolate.execution.projections/15.1.10", "hashPath": "hotchocolate.execution.projections.15.1.10.nupkg.sha512"}, "HotChocolate.Features/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-QGdEr7EukZOFKWwxMKNwNDU0yPPbNyJq7zkvIysHP6snVNWd2RbBMtaPqtPGPwZnoL7TtZmS8VJB6HZl4R/QXQ==", "path": "hotchocolate.features/15.1.10", "hashPath": "hotchocolate.features.15.1.10.nupkg.sha512"}, "HotChocolate.Fetching/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-40SktBr19FBfB0PTsZuyK3VtO/lk7xly74ZPNOTqbRABPt4folzm/w3GOBP6Wc4+rV9Pq+7O1z77XUAYEYFATw==", "path": "hotchocolate.fetching/15.1.10", "hashPath": "hotchocolate.fetching.15.1.10.nupkg.sha512"}, "HotChocolate.Language/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-FilQxQbNtLrMHzSm2/Zq140gxmBIicsXXIQuE1LZRfeQjDwSk0ywK9Ap2XWsRhSMZ2gaWwdYxH0aWrj22EaAUQ==", "path": "hotchocolate.language/15.1.10", "hashPath": "hotchocolate.language.15.1.10.nupkg.sha512"}, "HotChocolate.Language.SyntaxTree/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-BDuvdKfXQxPzddVDsy5Q9E/PnVXlA4Dnkt4KEuhlBiVyQB2TaWj/xofNysnO5PJCMvsNWDzt2wGuthgsSJjaoQ==", "path": "hotchocolate.language.syntaxtree/15.1.10", "hashPath": "hotchocolate.language.syntaxtree.15.1.10.nupkg.sha512"}, "HotChocolate.Language.Utf8/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-dLzQrebwg/KHUcwYFBK6iZCL+FCGYyAwtEgDFDBBYfAw6NF2wegBovYSaYhEgWGX6TgzG7OjWReFtdypZViBIw==", "path": "hotchocolate.language.utf8/15.1.10", "hashPath": "hotchocolate.language.utf8.15.1.10.nupkg.sha512"}, "HotChocolate.Language.Visitors/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-ZXI7r1Rh0qzYuzNFhzf5+5MmxB6XcZ210JPAQoyWbLnMU7n4oL7OaS47/bcUgE4dvqYbYubevaeWN1apl8xD/Q==", "path": "hotchocolate.language.visitors/15.1.10", "hashPath": "hotchocolate.language.visitors.15.1.10.nupkg.sha512"}, "HotChocolate.Language.Web/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-1amXpTbrEJAQytx7Abo0SBSo7BheFW2y30ffcjQ0LyzvHNYpizBZEUBacuyfJ80SMlKCKiOTGY+sADw8a1Y3+w==", "path": "hotchocolate.language.web/15.1.10", "hashPath": "hotchocolate.language.web.15.1.10.nupkg.sha512"}, "HotChocolate.Primitives/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-qhOTNSTZEVSmrExPVevOBglTNdtwd3AbCxTKCQMo34R8Jg2QyNV/kYPWxXtKML5kHrCZ6NSJ5q9W50iFppiVwQ==", "path": "hotchocolate.primitives/15.1.10", "hashPath": "hotchocolate.primitives.15.1.10.nupkg.sha512"}, "HotChocolate.Subscriptions/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-POvGyv4d8MY6HQHFgWqYgmxaIRviI8OWRdrhCqXJc++cx56FEnL0TAL5ELiOWlGA/nhkEDt5go5Ac04v8Ha0gg==", "path": "hotchocolate.subscriptions/15.1.10", "hashPath": "hotchocolate.subscriptions.15.1.10.nupkg.sha512"}, "HotChocolate.Subscriptions.InMemory/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-WVqky4G0FEuzn5G4goVRfJYYL8I0CHf93ovby7M0uCHmhPtrWAbnuyFGRYGFq67FQsP4zfeALW8r0qG6+BeM6Q==", "path": "hotchocolate.subscriptions.inmemory/15.1.10", "hashPath": "hotchocolate.subscriptions.inmemory.15.1.10.nupkg.sha512"}, "HotChocolate.Transport.Sockets/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-m0mnFVRgKmKWDlnTvkP5TPPWld9CJS1mdeVGdM5SjevTlCA3NUpdvcGRPNC3znWa56emf8hTv96ozH/KvRwOCQ==", "path": "hotchocolate.transport.sockets/15.1.10", "hashPath": "hotchocolate.transport.sockets.15.1.10.nupkg.sha512"}, "HotChocolate.Types/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-fD1pVNtKqEq0IC/foTkzwPy5lCwuiLNNydCVi6EStdEI7BwYohxnYoSH8/zcyveaXAeWfoy/7eHVxCC4NZFBWA==", "path": "hotchocolate.types/15.1.10", "hashPath": "hotchocolate.types.15.1.10.nupkg.sha512"}, "HotChocolate.Types.CursorPagination/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-CqJfHIhzF1Vd+SsO5m3Sp/2vyMQwiUhB0W0ol5rHqzcMJmarAmI0HruoUU1rej5lBPAjogaTUukBTgpwJoORhA==", "path": "hotchocolate.types.cursorpagination/15.1.10", "hashPath": "hotchocolate.types.cursorpagination.15.1.10.nupkg.sha512"}, "HotChocolate.Types.CursorPagination.Extensions/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-J2+PPrJMZwbLdWGa4Du3zz9HOIUyhKWgs5sEWJd1EDPpCZxAyytiObrP+RBgnL6h+ePzydK657+2FViegjSTEA==", "path": "hotchocolate.types.cursorpagination.extensions/15.1.10", "hashPath": "hotchocolate.types.cursorpagination.extensions.15.1.10.nupkg.sha512"}, "HotChocolate.Types.Errors/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-8gab6Eic51hNWkmSMcK4ugUAgGmmQ6Z+GqfdTl02jfIJxIMUpdUm2bDcy0boxcxbnXYO82HK+K3gxafl+K7haw==", "path": "hotchocolate.types.errors/15.1.10", "hashPath": "hotchocolate.types.errors.15.1.10.nupkg.sha512"}, "HotChocolate.Types.Mutations/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-m6l7jUZKnZTfDX2FPqa9z4Aq9vVdfIAvh/nhG0gBmZTTPGwDbLNecPjAPuZCMpL9ZQkluXOWyZFmPA5mtBDgUA==", "path": "hotchocolate.types.mutations/15.1.10", "hashPath": "hotchocolate.types.mutations.15.1.10.nupkg.sha512"}, "HotChocolate.Types.Queries/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-jRcFWIyqDaV8nx7J5+18HLLKOU+T3seSCjkhs4rLhuIPaLaO3wFPCvDa61tAFh9EjkUaLxOq4GBvqHdNufyBTw==", "path": "hotchocolate.types.queries/15.1.10", "hashPath": "hotchocolate.types.queries.15.1.10.nupkg.sha512"}, "HotChocolate.Types.Scalars.Upload/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-h7q3/VFwc3+a65UdJLMIgg9h18ZhL9Qib8yNN0/ZnuVxE3aHdLhClEW+EFB3BVdFKUjUCJ7WkVT0n7+9jGBHsw==", "path": "hotchocolate.types.scalars.upload/15.1.10", "hashPath": "hotchocolate.types.scalars.upload.15.1.10.nupkg.sha512"}, "HotChocolate.Types.Shared/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-7+OLGkTVuee/m/Z5Yn3qe/YLPMDsaB+e2reFLuiuPIFdHWj+jGqsQ/S4E1THBMddw2rJQamwCsPBPLRcMH51TA==", "path": "hotchocolate.types.shared/15.1.10", "hashPath": "hotchocolate.types.shared.15.1.10.nupkg.sha512"}, "HotChocolate.Utilities/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-tejOvFiLErWucVaq0dSGYGK3/2ZS2JWgzow3wn9qapjzYVObUOiATc5mfWjMjgac0FkeQH6dkZBkc5n2QfYp6w==", "path": "hotchocolate.utilities/15.1.10", "hashPath": "hotchocolate.utilities.15.1.10.nupkg.sha512"}, "HotChocolate.Utilities.DependencyInjection/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-EFuFEmrrgGrXLTcp69GuWdVjbWdoT0PdNZS5Ei3iRMVySjxuoAopthHITKom/3xw2emT8+J2y7MUK6OmctMzRw==", "path": "hotchocolate.utilities.dependencyinjection/15.1.10", "hashPath": "hotchocolate.utilities.dependencyinjection.15.1.10.nupkg.sha512"}, "HotChocolate.Validation/15.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-9wGsahoTEXypQXLusLbnClcFx8XzTmso2OiuwK1R7v6pDCddltl6OkACoITLu3ZwyqYZHIUui8MH6TjTdouTvA==", "path": "hotchocolate.validation/15.1.10", "hashPath": "hotchocolate.validation.15.1.10.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/8.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-W3R++XjVxo/hFldbg0IGAFeTA28M3Vagml4/GIIYC/5wYKrO4fdVM2+y/V6Bu6GEHe6OL8E6gcBqPod1OjkoeQ==", "path": "microsoft.aspnetcore.openapi/8.0.19", "hashPath": "microsoft.aspnetcore.openapi.8.0.19.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-+pz7gIPh5ydsBcQvivt4R98PwJXer86fyQBBToIBLxZ5kuhW4N13Ijz87s9WpuPtF1vh4JesYCgpDPAOgkMhdg==", "path": "microsoft.data.sqlclient/5.1.6", "hashPath": "microsoft.data.sqlclient.5.1.6.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-wNGM5ZTQCa2blc9ikXQouybGiyMd6IHPVJvAlBEPtr6JepZEOYeDxGyprYvFVeOxlCXs7avridZQ0nYkHzQWCQ==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-zkt5yQgnpWKX3rOxn+ZcV23Aj0296XCTqg4lx1hKY+wMXBgkn377UhBrY/A4H6kLpNT7wqZN98xCV0YHXu9VRA==", "path": "microsoft.entityframeworkcore/9.0.9", "hashPath": "microsoft.entityframeworkcore.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-QdM2k3Mnip2QsaxJbCI95dc2SajRMENdmaMhVKj4jPC5dmkoRcu3eEdvZAgDbd4bFVV1jtPGdHtXewtoBMlZqA==", "path": "microsoft.entityframeworkcore.abstractions/9.0.9", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-uiKeU/qR0YpaDUa4+g0rAjKCuwfq8YWZGcpPptnFWIr1K7dXQTm/15D2HDwwU4ln3Uf66krYybymuY58ua4hhw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.9", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-cFx<PERSON>70tohWe3ugCjLhZB01mR7WHpg5dEK6zHsbkDFfpLxWT+HoZQKgchTJgF4bPWBPTyrlYlqfPY212fFtmJjg==", "path": "microsoft.entityframeworkcore.design/9.0.9", "hashPath": "microsoft.entityframeworkcore.design.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-SonFU9a8x4jZIhIBtCw1hIE3QKjd4c7Y3mjptoh682dfQe7K9pUPGcEV/sk4n8AJdq4fkyJPCaOdYaObhae/Iw==", "path": "microsoft.entityframeworkcore.relational/9.0.9", "hashPath": "microsoft.entityframeworkcore.relational.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-t+6Zo92F5CgKyFncPSWRB3DFNwBrGug9F6rlrUFlJEr4Bf0t4ZFhZLg0qfuA3ouT7AQKuLTrvXLxuov8DWcuPQ==", "path": "microsoft.entityframeworkcore.sqlserver/9.0.9", "hashPath": "microsoft.entityframeworkcore.sqlserver.9.0.9.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-Q8n1PXXJApa1qX8HI3r/YuHoJ1HuLwjI2hLqaCV9K9pqQhGpi6Z38laOYwL2ElUOTWCxTKMDEMMYWfPlw6rwgg==", "path": "microsoft.entityframeworkcore.tools/9.0.9", "hashPath": "microsoft.entityframeworkcore.tools.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-NgtRHOdPrAEacfjXLSrH/SRrSqGf6Vaa6d16mW2yoyJdg7AJr0BnBvxkv7PkCm/CHVyzojTK7Y+oUDEulqY1Qw==", "path": "microsoft.extensions.caching.abstractions/9.0.9", "hashPath": "microsoft.extensions.caching.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-ln31BtsDsBQxykJgxuCtiUXWRET9FmqeEq0BpPIghkYtGpDDVs8ZcLHAjCCzbw6aGoLek4Z7JaDjSO/CjOD0iw==", "path": "microsoft.extensions.caching.memory/9.0.9", "hashPath": "microsoft.extensions.caching.memory.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-p5RKAY9POvs3axwA/AQRuJeM8AHuE8h4qbP1NxQeGm0ep46aXz1oCLAp/oOYxX1GsjStgdhHrN3XXLLXr0+b3w==", "path": "microsoft.extensions.configuration.abstractions/9.0.9", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-zQV2WOSP+3z1EuK91ULxfGgo2Y75bTRnmJHp08+w/YXAyekZutX/qCd88/HOMNh35MDW9mJJJxPpMPS+1Rww8A==", "path": "microsoft.extensions.dependencyinjection/9.0.9", "hashPath": "microsoft.extensions.dependencyinjection.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-/hymojfWbE9AlDOa0mczR44m00Jj+T3+HZO0ZnVTI032fVycI0ZbNOVFP6kqZMcXiLSYXzR2ilcwaRi6dzeGyA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.9", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-fNGvKct2De8ghm0Bpfq0iWthtzIWabgOTi+gJhNOPhNJIowXNEUE2eZNW/zNCzrHVA3PXg2yZ+3cWZndC2IqYA==", "path": "microsoft.extensions.dependencymodel/9.0.9", "hashPath": "microsoft.extensions.dependencymodel.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-MaCB0Y9hNDs4YLu3HCJbo199WnJT8xSgajG1JYGANz9FkseQ5f3v/llu3HxLI6mjDlu7pa7ps9BLPWjKzsAAzQ==", "path": "microsoft.extensions.logging/9.0.9", "hashPath": "microsoft.extensions.logging.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-FEgpSF+Z9StMvrsSViaybOBwR0f0ZZxDm8xV5cSOFiXN/t+ys+rwAlTd/6yG7Ld1gfppgvLcMasZry3GsI9lGA==", "path": "microsoft.extensions.logging.abstractions/9.0.9", "hashPath": "microsoft.extensions.logging.abstractions.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4pm+XgxSukskwjzDDfSjG4KNUIOdFF2VaqZZDtTzoyQMOVSnlV6ZM8a9aVu5dg9LVZTB//utzSc8fOi0b0Mb2Q==", "path": "microsoft.extensions.objectpool/8.0.0", "hashPath": "microsoft.extensions.objectpool.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-loxGGHE1FC2AefwPHzrjPq7X92LQm64qnU/whKfo6oWaceewPUVYQJBJs3S3E2qlWwnCpeZ+dGCPTX+5dgVAuQ==", "path": "microsoft.extensions.options/9.0.9", "hashPath": "microsoft.extensions.options.9.0.9.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-z4pyMePOrl733ltTowbN565PxBw1oAr8IHmIXNDiDqd22nFpYltX9KhrNC/qBWAG1/Zx5MHX+cOYhWJQYCO/iw==", "path": "microsoft.extensions.primitives/9.0.9", "hashPath": "microsoft.extensions.primitives.9.0.9.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "path": "microsoft.identitymodel.logging/6.35.0", "hashPath": "microsoft.identitymodel.logging.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "path": "microsoft.identitymodel.protocols/6.35.0", "hashPath": "microsoft.identitymodel.protocols.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "path": "microsoft.identitymodel.tokens/6.35.0", "hashPath": "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.14": {"type": "package", "serviceable": true, "sha512": "sha512-tTaBT8qjk3xINfESyOPE2rIellPvB7qpVqiWiyA/lACVvz+xOGiXhFUfohcx82NLbi5avzLW0lx+s6oAqQijfw==", "path": "microsoft.openapi/1.6.14", "hashPath": "microsoft.openapi.1.6.14.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-+NB4UYVYN6AhDSjW0IJAd1AGD8V33gemFNLPaxKTtPkHB+HaKAKf9MGAEUPivEWvqeQfcKIw8lJaHq6LHljRuw==", "path": "swashbuckle.aspnetcore/6.6.2", "hashPath": "swashbuckle.aspnetcore.6.6.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-ovgPTSYX83UrQUWiS5vzDcJ8TEX1MAxBgDFMK45rC24MorHEPQlZAHlaXj/yth4Zf6xcktpUgTEBvffRQVwDKA==", "path": "swashbuckle.aspnetcore.swagger/6.6.2", "hashPath": "swashbuckle.aspnetcore.swagger.6.6.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-zv4ikn4AT1VYuOsDCpktLq4QDq08e7Utzbir86M5/ZkRaLXbCPF11E1/vTmOiDzRTl0zTZINQU2qLKwTcHgfrA==", "path": "swashbuckle.aspnetcore.swaggergen/6.6.2", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.6.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-mBBb+/8Hm2Q3Wygag+hu2jj69tZW5psuv0vMRXY07Wy+Rrj40vRP8ZTbKBhs91r45/HXT4aY4z0iSBYx1h6JvA==", "path": "swashbuckle.aspnetcore.swaggerui/6.6.2", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.6.2.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections.Immutable/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "path": "system.collections.immutable/7.0.0", "hashPath": "system.collections.immutable.7.0.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-8hy61dsFYYSDjT9iTAfygGMU3A0EAnG69x5FUXeKsCjMhBmtTBt4UMUEW3ipprFoorOW6Jw/7hDMjXtlrsOvVQ==", "path": "system.diagnostics.diagnosticsource/9.0.9", "hashPath": "system.diagnostics.diagnosticsource.9.0.9.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-hnQCFWPAvZM45fFEExgbHTgq6GyfyQdHxyI+PvuzqI1G7KvBYcnNEPHbLJ+1jP+Ip69yBvvUOxaibmDInmOw2Q==", "path": "system.formats.asn1/9.0.9", "hashPath": "system.formats.asn1.9.0.9.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "path": "system.identitymodel.tokens.jwt/6.35.0", "hashPath": "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512"}, "System.IO.Hashing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ne1843evDugl0md7Fjzy6QjJrzsjh46ZKbhf8GwBXb5f/gw97J4bxMs0NQKifDuThh/f0bZ0e62NPl1jzTuRqA==", "path": "system.io.hashing/8.0.0", "hashPath": "system.io.hashing.8.0.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-VySjpsCLprojvat550Flrm3NQB982CPuDzILajqjQihFmrQXZPdQyktIbcpVPJyaExFYtAfY1DpwMdWQuS0kbw==", "path": "system.io.pipelines/9.0.9", "hashPath": "system.io.pipelines.9.0.9.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection.Metadata/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "path": "system.reflection.metadata/7.0.0", "hashPath": "system.reflection.metadata.7.0.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-bzYTmAcmfelUOCBxvbgsfSr2tq94ydA2gJZAxZRcuNa0LlmlVz8JNHst6RG1qsDujyVYT4vjv06y8sCLbvCXdg==", "path": "system.text.encodings.web/9.0.9", "hashPath": "system.text.encodings.web.9.0.9.nupkg.sha512"}, "System.Text.Json/9.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-NEnpppwq67fRz/OvQRxsEMgetDJsxlxpEsAFO/4PZYbAyAMd4Ol6KS7phc8uDoKPsnbdzRLKobpX303uQwCqdg==", "path": "system.text.json/9.0.9", "hashPath": "system.text.json.9.0.9.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "Yarp.ReverseProxy/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-gxtkN3a+9biu9V9Zd5NaTO6VZWXAnS2mhQ0R/VXmSPoTuiQNZsakKikrKpDtKxrL5nUYzbRsHtl40WNq+ZBKKg==", "path": "yarp.reverseproxy/2.3.0", "hashPath": "yarp.reverseproxy.2.3.0.nupkg.sha512"}}}