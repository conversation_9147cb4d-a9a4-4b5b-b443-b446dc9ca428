{"$schema": "http://json-schema.org/draft-04/schema#", "definitions": {"webOptimizer": {"title": "web optimizer", "type": "object", "description": "Settings for WebOptimizer.Core", "properties": {"enableCaching": {"description": "Determines if the \"cache-control\" HTTP headers should be set and if conditional GET (304) requests should be supported. This could be helpful to disable while in development mode.", "type": "boolean"}, "enableTagHelperBundling": {"description": "Determines if `<script>` and `<link>` elements should point to the bundled path or a reference per source file should be created. This is helpful to disable when in development mode.", "type": "boolean", "default": true}}}, "cdn": {"title": "CDN", "type": "object", "description": "Definitions for WebEssentials.AspNetCore.CdnTagHelpers", "properties": {"url": {"description": "An absolute URL used as a prefix for static resources", "type": "string", "pattern": "^((//|https?://).+|)$"}, "prefetch": {"description": "If true, injects a <link rel='dns-prefetch'> tag that speeds up DNS resolution to the CDN.", "type": "boolean", "default": true}}}, "pwa": {"properties": {"cacheId": {"description": "The cache identifier of the service worker (can be any string). Change this property to force the service worker to reload in browsers.", "type": "string", "default": "v1.0"}, "offlineRoute": {"description": "The route to the page to show when offline.", "type": "string", "default": "/offline.html"}, "registerServiceWorker": {"description": "Determines if a script that registers the service worker should be injected into the bottom of the HTML page.", "type": "boolean", "default": true}, "registerWebmanifest": {"description": "Determines if a meta tag that points to the web manifest should be inserted at the end of the head element.", "type": "boolean", "default": true}, "routesToPreCache": {"description": "A comma separated list of routes to pre-cache when service worker installs in the browser.", "type": "string", "default": ""}, "strategy": {"description": "Selects one of the predefined service worker types.", "enum": ["cacheFirst", "cacheFirstSafe", "minimal", "networkFirst"], "default": "cacheFirstSafe"}}}, "ElmahIo": {"properties": {"ApiKey": {"description": "An elmah.io API key with the Messages | Write permission.", "type": "string", "pattern": "^([0-9a-f]{32})|(#\\{.*\\}#?)$"}, "LogId": {"description": "The Id of the elmah.io log to store messages in.", "type": "string", "pattern": "^([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})|(#\\{.*\\}#?)$"}, "Application": {"description": "An application name to put on all error messages.", "type": "string"}, "HandledStatusCodesToLog": {"description": "A list of HTTP status codes (besides 404) to log even though no exception is thrown.", "type": "array", "items": {"type": "integer"}}, "TreatLoggingAsBreadcrumbs": {"description": "Include log messages from Microsoft.Extensions.Logging as breadcrumbs.", "type": "boolean"}, "HeartbeatId": {"description": "The Id of the elmah.io heartbeat to notify.", "type": "string", "pattern": "^([0-9a-f]{32})|(#\\{.*\\}#?)$"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "LogId"]}, "protocols": {"description": "The protocols enabled on the endpoint.", "type": "string", "enum": ["None", "Http1", "Http2", "Http1AndHttp2", "Http3", "Http1AndHttp2AndHttp3"]}, "certificate": {"title": "certificate", "description": "Certificate configuration.", "type": "object", "properties": {"Path": {"description": "The certificate file path. If a file path is specified then the certificate will be loaded from the file system.", "type": "string"}, "KeyPath": {"description": "The certificate key file path. Available in .NET 5 and later.", "type": "string"}, "Password": {"description": "The certificate password used to access the private key.", "type": "string"}, "Subject": {"description": "The certificate subject. If a subject is specified then the certificate will be loaded from the certificate store.", "type": "string"}, "Store": {"description": "The certificate store name. Defaults to '<PERSON>'.", "type": "string", "default": "My"}, "Location": {"description": "The certificate store location. Defaults to 'CurrentUser'.", "type": "string", "enum": ["LocalMachine", "CurrentUser"], "default": "CurrentUser"}, "AllowInvalid": {"description": "A value indicating whether or not to load certificates that are considered invalid. Defaults to false.", "type": "boolean", "default": false}}}, "sslProtocols": {"description": "Specifies allowable SSL protocols. Defaults to 'None' which allows the operating system to choose the best protocol to use, and to block protocols that are not secure. Unless your app has a specific reason not to, you should use this default. Available in .NET 5 and later.", "type": "array", "items": {"type": "string", "enum": ["None", "Tls", "Tls11", "Tls12", "Tls13"], "default": "None"}}, "clientCertificateMode": {"description": "Specifies the client certificate requirements for a HTTPS connection. Defaults to 'NoCertificate'. Available in .NET 5 and later.", "type": "string", "enum": ["NoCertificate", "AllowCertificate", "RequireCertificate"], "default": "NoCertificate"}, "kestrel": {"title": "kestrel", "type": "object", "description": "ASP.NET Core Kestrel server configuration.", "properties": {"Endpoints": {"title": "endpoints", "description": "Endpoints that Kestrel listens to for network requests. Each endpoint has a name specified by its JSON property name.", "type": "object", "additionalProperties": {"title": "endpoint options", "description": "Kestrel endpoint configuration.", "type": "object", "properties": {"Url": {"description": "The scheme, host name, and port the endpoint will listen on. A Url is required.", "type": "string", "format": "uri"}, "Protocols": {"$ref": "#/definitions/protocols"}, "SslProtocols": {"$ref": "#/definitions/sslProtocols"}, "Certificate": {"$ref": "#/definitions/certificate"}, "ClientCertificateMode": {"$ref": "#/definitions/clientCertificateMode"}, "Sni": {"title": "SNI", "description": "Server Name Indication (SNI) configuration. This enables the mapping of client requested host names to certificates and other TLS settings. Wildcard names prefixed with '*.', as well as a top level '*' are supported. Available in .NET 5 and later.", "type": "object", "additionalProperties": {"title": "SNI options", "description": "Endpoint SNI configuration.", "type": "object", "properties": {"Protocols": {"$ref": "#/definitions/protocols"}, "SslProtocols": {"$ref": "#/definitions/sslProtocols"}, "Certificate": {"$ref": "#/definitions/certificate"}, "ClientCertificateMode": {"$ref": "#/definitions/clientCertificateMode"}}}}}, "required": ["Url"]}}, "EndpointDefaults": {"title": "endpoint defaults", "description": "Default configuration applied to all endpoints. Named endpoint specific configuration overrides defaults.", "type": "object", "properties": {"Protocols": {"$ref": "#/definitions/protocols"}, "SslProtocols": {"$ref": "#/definitions/sslProtocols"}, "ClientCertificateMode": {"$ref": "#/definitions/clientCertificateMode"}}}, "Certificates": {"title": "certificates", "description": "Certificates that Kestrel uses with HTTPS endpoints. Each certificate has a name specified by its JSON property name. The 'Default' certificate is used by HTTPS endpoints that haven't specified a certificate.", "type": "object", "additionalProperties": {"$ref": "#/definitions/certificate"}}}}, "logLevelThreshold": {"description": "Log level threshold.", "type": "string", "enum": ["Trace", "Debug", "Information", "Warning", "Error", "Critical", "None"]}, "logLevel": {"title": "logging level options", "description": "Log level configurations used when creating logs. Only logs that exceeds its matching log level will be enabled. Each log level configuration has a category specified by its JSON property name. For more information about configuring log levels, see https://docs.microsoft.com/aspnet/core/fundamentals/logging/#configure-logging.", "type": "object", "additionalProperties": {"$ref": "#/definitions/logLevelThreshold"}}, "logging": {"title": "logging options", "type": "object", "description": "Configuration for Microsoft.Extensions.Logging.", "properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}, "Console": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}, "FormatterName": {"description": "Name of the log message formatter to use. Defaults to 'simple'.", "type": "string", "default": "simple"}, "FormatterOptions": {"title": "formatter options", "description": "Log message formatter options. Additional properties are available on the options depending on the configured formatter. The formatter is specified by FormatterName.", "type": "object", "properties": {"IncludeScopes": {"description": "Include scopes when true. Defaults to false.", "type": "boolean", "default": false}, "TimestampFormat": {"description": "Format string used to format timestamp in logging messages. Defaults to null.", "type": "string"}, "UseUtcTimestamp": {"description": "Indication whether or not UTC timezone should be used to for timestamps in logging messages. Defaults to false.", "type": "boolean", "default": false}}}, "LogToStandardErrorThreshold": {"$ref": "#/definitions/logLevelThreshold", "description": "The minimum level of messages are written to Console.Error."}}}, "EventSource": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}, "Debug": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}, "EventLog": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}, "ElmahIo": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}, "ElmahIoBreadcrumbs": {"properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}}, "additionalProperties": {"title": "provider logging settings", "type": "object", "description": "Logging configuration for a provider. The provider name must match the configuration's JSON property property name.", "properties": {"LogLevel": {"$ref": "#/definitions/logLevel"}}}}, "allowedHosts": {"description": "ASP.NET Core host filtering middleware configuration. Allowed hosts is a semicolon-delimited list of host names without port numbers. Requests without a matching host name will be refused. Host names may be prefixed with a '*.' wildcard, or use '*' to allow all hosts.", "type": "string"}, "connectionStrings": {"title": "connection string options", "description": "Connection string configuration. Get connection strings with the IConfiguration.GetConnectionString(string) extension method.", "type": "object", "additionalProperties": {"description": "Connection string configuration. Each connection string has a name specified by its JSON property name.", "type": "string"}}, "NLog": {"title": "NLog options", "type": "object", "description": "NLog configuration", "default": {}, "properties": {"autoReload": {"type": "boolean", "description": "Automatically reload the NLog configuration when notified that appsettings.json file has changed.", "default": false}, "throwConfigExceptions": {"type": ["boolean", "null"], "description": "Throws an exception when there is a config error? If not set, then throwExceptions will be used for this setting.", "default": false}, "throwExceptions": {"type": "boolean", "description": "Throws an exception when there is an error. For unit testing only and advanced troubleshooting.", "default": false}, "internalLogLevel": {"type": "string", "description": "The minimal log level for the internal logger.", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal", "Off"], "default": "Off"}, "internalLogFile": {"type": "string", "description": "Write internal log to the specified filepath"}, "internalLogToConsole": {"type": "boolean", "description": "Write internal log to a console", "default": "false"}, "internalLogToConsoleError": {"type": "boolean", "description": "Write internal log to a console with error stream", "default": "false"}, "globalThreshold": {"type": "string", "description": "Log events below this threshold are not logged.", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal", "Off"], "default": "Off"}, "autoShutdown": {"type": "boolean", "description": "Automatically call `LogFactory.Shutdown` on AppDomain.Unload or AppDomain.ProcessExit", "default": "true"}, "extensions": {"type": "array", "description": "Load NLog extension packages for additional targets and layouts", "default": [], "items": {"title": "extension", "type": "object", "description": "", "default": {}, "properties": {"assembly": {"type": "string", "description": "Assembly Name of the NLog extension package."}, "prefix": {"type": "string", "description": "Appends prefix to all type-names loaded from the assembly", "default": ""}, "assemblyFile": {"type": "string", "description": "Absolute filepath to the Assembly-file of the NLog extension package.", "default": ""}}}}, "variables": {"title": "variables", "type": "object", "description": "Key-value pair of variables", "propertyNames": {"pattern": "^[A-Za-z0-9_.-]+$"}, "patternProperties": {".*": {"type": ["number", "string", "boolean"]}}}, "targetDefaultWrapper": {"title": "default wrapper", "type": "object", "description": "Wrap all defined targets with this custom target wrapper.", "default": {}, "required": ["type"], "properties": {"type": {"type": "string", "description": ""}}}, "targets": {"title": "targets", "type": "object", "description": "", "default": {}, "properties": {"async": {"type": "boolean", "description": "Wrap all defined targets using AsyncWrapper with OverflowAction=Discard for better performance."}}}, "rules": {"oneOf": [{"type": "array", "description": "", "default": [], "items": {"$ref": "#/definitions/NLogRulesItem"}}, {"title": "rules", "type": "object", "propertyNames": {"pattern": "^[0-9]+$"}, "patternProperties": {".*": {"$ref": "#/definitions/NLogRulesItem"}}}]}}}, "NLogRulesItem": {"title": "NLog rule item", "type": "object", "description": "Redirect LogEvents from matching Logger objects to specified targets", "default": {}, "required": ["logger"], "properties": {"logger": {"type": "string", "description": "Match Logger objects based on their Logger-name. Can use wildcard characters ('*' or '?')."}, "ruleName": {"type": "string", "description": "Rule identifier to allow rule lookup with Configuration.FindRuleByName and Configuration.RemoveRuleByName."}, "level": {"anyOf": [{"type": "string", "description": "", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal"]}, {"type": "string"}]}, "levels": {"type": "string", "description": "Comma separated list of levels that this rule matches."}, "minLevel": {"anyOf": [{"type": "string", "description": "", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal"]}, {"type": "string"}]}, "maxLevel": {"anyOf": [{"type": "string", "description": "", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal"]}, {"type": "string"}]}, "finalMinLevel": {"anyOf": [{"type": "string", "description": "", "enum": ["Trace", "Debug", "Info", "<PERSON><PERSON>", "Error", "Fatal"]}, {"type": "string"}]}, "writeTo": {"type": "string", "description": "Name or names of a target - separated by comma. Remove this property for sending events to the blackhole."}, "final": {"type": "boolean", "description": "Ignore further rules if this one matches.", "default": false}, "enabled": {"type": "boolean", "description": "", "default": true}, "filters": {"oneOf": [{"type": "array", "description": "", "default": [], "items": {"title": "filter", "type": "object", "description": "", "default": {}, "required": ["type"], "properties": {"type": {"type": "string", "description": ""}, "action": {"type": "string", "description": "Result action when filter matches logevent.", "enum": ["Neutral", "Log", "Ignore", "LogFinal", "IgnoreFinal"], "default": "Neutral"}}}}, {"title": "filter", "type": "object", "description": "", "default": {}}]}, "filterDefaultAction": {"type": "string", "description": "Default action if none of the filters match.", "enum": ["Neutral", "Log", "Ignore", "LogFinal", "IgnoreFinal"], "default": "Ignore"}}}, "Serilog": {"type": "object", "title": "Serilog appSettings", "description": "Serilog appSettings Configuration", "properties": {"$schema": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "description": "Pointer to the schema against which this document should be validated."}, "Using": {"type": "array", "title": "List of Auto-discovery of configuration assemblies", "description": "Using section contains list of assemblies in which configuration methods. Can be required depending of the project type: See: https://github.com/serilog/serilog-settings-configuration#using-section-and-auto-discovery-of-configuration-assemblies", "uniqueItems": true, "items": {"$ref": "#/definitions/Serilog/definitions/AssemblyReference"}}, "LevelSwitches": {"type": "object", "patternProperties": {"^(?<SerilogLevelSwitcherName>\\${0,1}[A-Za-z]+[A-Za-z0-9]*)$": {"$ref": "#/definitions/Serilog/definitions/SerilogLogEventLevel"}}, "additionalProperties": false}, "FilterSwitches": {"type": "object", "patternProperties": {"^(?<SerilogLevelSwitcherName>\\${0,1}[A-Za-z]+[A-Za-z0-9]*)$": {"type": "string"}}, "additionalProperties": false}, "MinimumLevel": {"type": ["string", "object"], "title": "Minimum LogLevel Threshold", "description": "Minimum LogLevel Threshold. (Support dynamic reload if the underlying IConfigurationProvider supports it)", "oneOf": [{"$ref": "#/definitions/Serilog/definitions/SerilogLogEventLevel"}, {"$ref": "#/definitions/Serilog/definitions/DetailedMinimumLevel"}]}, "Properties": {"type": "object", "title": "Log events Properties", "description": "This section defines a static list of key-value pairs that will enrich log events.", "additionalProperties": {"type": "string"}}, "Enrich": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReference"}], "title": "Log events Enriches", "description": "This section defines Enriches that will be applied to log events."}, "Destructure": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReference"}], "title": "Log events Destructure", "description": "This section defines Destructure."}, "Filter": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReference"}], "title": "Log events filters", "description": "This section defines filters that will be applied to log events."}, "WriteTo": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReference"}], "title": "Configuration for log destination", "description": "This section configures the sinks that log events will be emitted to."}, "AuditTo": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReference"}], "title": "Configuration for log destination for auditing", "description": "This section configures sinks for auditing, instead of regular (safe) logging. Obs: When auditing is used, exceptions from sinks and any intermediate filters propagate back to the caller."}}, "patternProperties": {"^Enrich:((?<EnvironmentVariableName>[a-zA-Z_]\\w*)|(?<ArrayIndex>\\d*))$": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}], "title": "Log events Enriches", "description": "This section defines Enriches that will be applied to log events."}, "^Destructure:((?<EnvironmentVariableName>[a-zA-Z_]\\w*)|(?<ArrayIndex>\\d*))$": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}], "title": "Log events Destructure", "description": "This section defines Destructure."}, "^Filter:((?<EnvironmentVariableName>[a-zA-Z_]\\w*)|(?<ArrayIndex>\\d*))$": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}], "title": "Log events filters", "description": "This section defines filters that will be applied to log events."}, "^WriteTo:((?<EnvironmentVariableName>[a-zA-Z_]\\w*)|(?<ArrayIndex>\\d*))$": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}], "title": "Configuration for log destination", "description": "This section configures the sinks that log events will be emitted to."}, "^AuditTo:((?<EnvironmentVariableName>[a-zA-Z_]\\w*)|(?<ArrayIndex>\\d*))$": {"allOf": [{"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}], "title": "Configuration for log destination for auditing", "description": "This section configures sinks for auditing, instead of regular (safe) logging. Obs: When auditing is used, exceptions from sinks and any intermediate filters propagate back to the caller."}}, "additionalProperties": false, "definitions": {"SerilogLogEventLevel": {"type": "string", "title": "Log level", "description": "Log level threshold.", "enum": ["Verbose", "Debug", "Information", "Warning", "Error", "Fatal"]}, "LoggingLevelSwitch": {"type": "string", "title": "LevelSwitches name", "description": "Log Level Switch string reference.", "pattern": "^(?<SerilogLevelSwitcherName>\\${0,1}[A-Za-z]+[A-Za-z0-9]*)$"}, "SerilogLogLevelThreshold": {"type": "string", "title": "Log Level or LevelSwitches name", "description": "A Serilog Log Level or a reference to a Log Level Switch name on `LevelSwitches` configuration.", "anyOf": [{"$ref": "#/definitions/Serilog/definitions/SerilogLogEventLevel"}, {"$ref": "#/definitions/Serilog/definitions/LoggingLevelSwitch"}]}, "DetailedMinimumLevel": {"type": "object", "title": "Detailed Log level.", "description": "Detailed Log level threshold object. Allowing set log levels be overridden per logging source.", "properties": {"Default": {"$ref": "#/definitions/Serilog/definitions/SerilogLogLevelThreshold"}, "ControlledBy": {"$ref": "#/definitions/Serilog/definitions/LoggingLevelSwitch"}, "Override": {"type": "object", "title": "Logging Source Log level object.", "description": "Set the Log level threshold or LevelSwitcher reference per Logging Source.", "additionalProperties": {"$ref": "#/definitions/Serilog/definitions/SerilogLogLevelThreshold"}}}, "additionalProperties": false}, "AssemblyReference": {"type": "string", "title": "Assembly Name", "description": ".NET Assembly Name, without the file extension", "minLength": 1, "pattern": "^(?<AssemblyName>\\S+)$"}, "ComplexMethodCallReference": {"type": "object", "properties": {"Name": {"$ref": "#/definitions/Serilog/definitions/CSharpMethodName"}, "Args": {"type": "object", "patternProperties": {"^(?<CSharpMethodArgumentName>[a-zA-Z_]\\w*)$": {}}, "additionalProperties": false}}, "additionalProperties": false, "required": ["Name"]}, "MethodCallReferenceItem": {"type": ["string", "object", "null"], "oneOf": [{"$ref": "#/definitions/Serilog/definitions/CSharpMethodName"}, {"$ref": "#/definitions/Serilog/definitions/ComplexMethodCallReference"}]}, "MethodCallReference": {"type": ["array", "string", "object"], "minLength": 1, "pattern": "^(?<CSharpMethodName>[a-zA-Z_]\\w*)$", "minItems": 1, "uniqueItems": true, "items": {"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}, "additionalProperties": {"$ref": "#/definitions/Serilog/definitions/MethodCallReferenceItem"}}, "CSharpMethodName": {"type": "string", "title": "Method Name", "description": "A name referring to a C# Class method", "minLength": 1, "pattern": "^(?<CSharpMethodName>[a-zA-Z_]\\w*)$"}, "CSharpMethodArgumentName": {"type": "string", "title": "Argument Name", "description": "A name referring to a C# Class method argument", "minLength": 1, "pattern": "^(?<CSharpMethodArgumentName>[a-zA-Z_]\\w*)$"}, "EnvironmentVariableName": {"type": "string", "title": "Environment Variable Name", "description": "A name referring to a OS Environment Variable", "minLength": 1, "pattern": "^(?<EnvironmentVariableName>[a-zA-Z_]\\w*)$"}, "SerilogLevelSwitcherName": {"type": "string", "title": "A Level Switcher Name", "description": "A name referring to a Serilog Settings Configuration Level Switcher", "minLength": 1, "pattern": "^(?<SerilogLevelSwitcherName>\\${0,1}[A-Za-z]+[A-Za-z0-9]*)$"}}}}, "id": "https://json.schemastore.org/appsettings.json", "patternProperties": {"^WebOptimizer$": {"$ref": "#/definitions/webOptimizer"}, "^webOptimizer$": {"$ref": "#/definitions/webOptimizer"}, "^weboptimizer$": {"$ref": "#/definitions/webOptimizer"}, "^(cdn|Cdn)$": {"$ref": "#/definitions/cdn"}, "^(pwa|PWA|Pwa)$": {"$ref": "#/definitions/pwa"}, "^(ElmahIo|Elmahio|elmahIo|elmahio)$": {"$ref": "#/definitions/ElmahIo"}, "^(nlog|Nlog|NLog)$": {"$ref": "#/definitions/NLog"}, "^(Serilog|serilog)$": {"$ref": "#/definitions/Serilog"}}, "properties": {"Kestrel": {"$ref": "#/definitions/kestrel"}, "Logging": {"$ref": "#/definitions/logging"}, "AllowedHosts": {"$ref": "#/definitions/allowedHosts"}, "ConnectionStrings": {"$ref": "#/definitions/connectionStrings"}, "ReverseProxy": {"type": "object", "description": "Reverse proxy configuration for routes and clusters.", "properties": {"Routes": {"type": "object", "description": "Named routes that direct incoming requests to clusters.", "patternProperties": {".": {"type": "object", "properties": {"ClusterId": {"type": "string", "description": "Name of the cluster this route points to."}, "Order": {"type": ["number", "null"], "description": "Order value for this route. Routes with lower numbers take precedence over higher numbers."}, "MaxRequestBodySize": {"type": ["number", "null"], "description": "An optional override for how large request bodies can be in bytes."}, "AuthorizationPolicy": {"type": ["string", "null"], "description": "Specifies which authorization policy applies, e.g. 'Default' or 'Anonymous'."}, "RateLimiterPolicy": {"type": ["string", "null"], "description": "The name of the RateLimiterPolicy to apply to this route."}, "OutputCachePolicy": {"type": ["string", "null"], "description": "The name of the OutputCachePolicy to apply to this route."}, "Timeout": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "The Timeout to apply to this route. This overrides any system defaults. Setting both Timeout and TimeoutPolicy is an error. Format: 'hh:mm:ss'."}, "TimeoutPolicy": {"type": ["string", "null"], "description": "Specifies which timeout policy applies, e.g. 'Default' or 'Disable'. Setting both Timeout and TimeoutPolicy is an error."}, "CorsPolicy": {"type": ["string", "null"], "description": "Specifies which CORS policy applies, e.g. 'Default' or 'Disable'."}, "Match": {"type": "object", "properties": {"Path": {"type": ["string", "null"], "description": "Path pattern using ASP.NET route template syntax, e.g. '/something/{**remainder}'."}, "Hosts": {"type": ["array", "null"], "description": "Only match requests with the given Host header. Supports wildcards and ports. For unicode host names, do not use punycode.", "items": {"type": "string"}}, "Methods": {"description": "Allowed HTTP methods.", "items": {"anyOf": [{"type": "string", "enum": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT", "TRACE"]}, {"type": ["array", "null"]}]}}, "Headers": {"type": ["array", "null"], "description": "List of header match conditions.", "items": {"type": "object", "properties": {"Name": {"type": "string", "description": "Header name."}, "Values": {"type": "array", "description": "Matches against any of these values.", "items": {"type": "string"}}, "Mode": {"type": "string", "description": "How the header values should be matched.", "enum": ["ExactHeader", "HeaderPrefix", "Contains", "NotContains", "Exists", "NotExists"]}, "IsCaseSensitive": {"type": "boolean"}}, "required": ["Name"], "additionalProperties": false}}, "QueryParameters": {"type": ["array", "null"], "description": "List of query string match conditions.", "items": {"type": "object", "properties": {"Name": {"type": "string", "description": "Name of the query parameter."}, "Values": {"type": "array", "description": "Matches against any of these values.", "items": {"type": "string"}}, "Mode": {"type": "string", "description": "How the query parameter values should be matched.", "enum": ["Exact", "Contains", "NotContains", "Prefix", "Exists"]}, "IsCaseSensitive": {"type": "boolean"}}, "required": ["Name"], "additionalProperties": false}}}, "additionalProperties": false, "anyOf": [{"required": ["Path"]}, {"required": ["Hosts"]}]}, "Metadata": {"type": ["object", "null"], "description": "Arbitrary key-value pairs for custom route logic.", "additionalProperties": {"type": "string"}}, "Transforms": {"type": ["array", "null"], "description": "List of transform objects for request customization.", "items": {"description": "A single transform definition.", "anyOf": [{"type": ["object", "null"], "$comment": "Fallback that matches any custom user-defined transforms.", "properties": {"RequestHeadersCopy": {"not": {}}, "RequestHeaderOriginalHost": {"not": {}}, "RequestHeader": {"not": {}}, "PathRemovePrefix": {"not": {}}, "PathSet": {"not": {}}, "PathPrefix": {"not": {}}, "QueryRouteParameter": {"not": {}}, "PathPattern": {"not": {}}, "QueryValueParameter": {"not": {}}, "QueryRemoveParameter": {"not": {}}, "HttpMethodChange": {"not": {}}, "RequestHeaderRouteValue": {"not": {}}, "RequestHeaderRemove": {"not": {}}, "RequestHeadersAllowed": {"not": {}}, "X-Forwarded": {"not": {}}, "Forwarded": {"not": {}}, "ClientCert": {"not": {}}, "ResponseHeadersCopy": {"not": {}}, "ResponseHeader": {"not": {}}, "ResponseHeaderRemove": {"not": {}}, "ResponseHeadersAllowed": {"not": {}}, "ResponseTrailersCopy": {"not": {}}, "ResponseTrailer": {"not": {}}, "ResponseTrailerRemove": {"not": {}}, "ResponseTrailersAllowed": {"not": {}}}}, {"type": "object", "description": "Sets whether incoming request headers are copied to the outbound request.", "properties": {"RequestHeadersCopy": {"type": "boolean", "description": "If true, copies all request headers to outbound request."}}, "additionalProperties": false, "required": ["RequestHeadersCopy"]}, {"type": "object", "description": "Specifies if the incoming request Host header should be copied to the proxy request.", "properties": {"RequestHeaderOriginalHost": {"type": "boolean", "description": "If true, preserve the original Host header; otherwise the destination's host is used."}}, "additionalProperties": false, "required": ["RequestHeaderOriginalHost"]}, {"type": "object", "description": "Transform for setting, appending, or removing a request header.", "properties": {"RequestHeader": {"type": "string", "description": "The header name to operate on."}, "Set": {"type": "string", "description": "Value to set the given request header to."}, "Append": {"type": "string", "description": "Value to append to the given request header."}, "Remove": {"type": "boolean", "description": "Removes the header if true."}}, "additionalProperties": false, "anyOf": [{"required": ["RequestHeader", "Set"]}, {"required": ["RequestHeader", "Append"]}, {"required": ["RequestHeader", "Remove"]}]}, {"type": "object", "description": "Transform that removes a specified prefix from the request path.", "properties": {"PathRemovePrefix": {"type": "string", "description": "Prefix to remove from the existing request path."}}, "additionalProperties": false, "required": ["PathRemovePrefix"]}, {"type": "object", "description": "Transform that replaces the entire request path with the provided value.", "properties": {"PathSet": {"type": "string", "description": "Sets the request path to this value."}}, "additionalProperties": false, "required": ["PathSet"]}, {"type": "object", "description": "Transform that adds the specified prefix to the request path.", "properties": {"PathPrefix": {"type": "string", "description": "Path prefix to add."}}, "additionalProperties": false, "required": ["PathPrefix"]}, {"type": "object", "description": "Transform that adds or replaces a query string parameter with a value from the route configuration.", "properties": {"QueryRouteParameter": {"type": "string", "description": "Specifies the query parameter name to add or replace."}, "Set": {"type": "string", "description": "Name of the route paramter to set the query parameter to."}, "Append": {"type": "string", "description": "Name of the route paramter to append to the query parameter."}}, "additionalProperties": false, "anyOf": [{"required": ["QueryRouteParameter", "Set"]}, {"required": ["QueryRouteParameter", "Append"]}]}, {"type": "object", "description": "Transform that replaces the entire request path using a pattern template, replacing {} segments with the route value.", "properties": {"PathPattern": {"type": "string", "description": "A path template starting with a '/', e.g. '/my/{plugin}/api/{**remainder}'."}}, "additionalProperties": false, "required": ["PathPattern"]}, {"type": "object", "description": "Adds or replaces parameters in the request query string.", "properties": {"QueryValueParameter": {"type": "string", "description": "Name of a query string parameter."}, "Set": {"type": "string", "description": "Value to set the given query parameter to."}, "Append": {"type": "string", "description": "Value to append to the given query parameter."}}, "additionalProperties": false, "anyOf": [{"required": ["QueryValueParameter", "Set"]}, {"required": ["QueryValueParameter", "Append"]}]}, {"type": "object", "description": "Removes the specified parameter from the request query string.", "properties": {"QueryRemoveParameter": {"type": "string", "description": "Name of a query string parameter."}}, "additionalProperties": false, "required": ["QueryRemoveParameter"]}, {"type": "object", "description": "Changes the http method used in the request.", "properties": {"HttpMethodChange": {"description": "The HTTP method to replace.", "anyOf": [{"type": "string", "enum": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT", "TRACE"]}, {"type": "string"}]}, "Set": {"type": "string", "description": "The new HTTP method."}}, "additionalProperties": false, "required": ["HttpMethodChange", "Set"]}, {"type": "object", "description": "Adds or replaces a header with a value from the route configuration.", "properties": {"RequestHeaderRouteValue": {"type": "string", "description": "The header name to operate on."}, "Set": {"type": "string", "description": "Route value to set the given header to."}, "Append": {"type": "string", "description": "Route value to append to the given header."}}, "additionalProperties": false, "anyOf": [{"required": ["RequestHeaderRouteValue", "Set"]}, {"required": ["RequestHeaderRouteValue", "Append"]}]}, {"type": "object", "description": "Removes the specified header from the request.", "properties": {"RequestHeaderRemove": {"type": "string", "description": "The header name."}}, "additionalProperties": false, "required": ["RequestHeaderRemove"]}, {"type": "object", "description": "YARP copies most request headers to the proxy request by default, this transform disables RequestHeadersCopy and only copies the given headers.", "properties": {"RequestHeadersAllowed": {"type": "string", "pattern": "^[a-zA-Z0-9!#$%&'*+-.^_`|~;]+$", "description": "A semicolon separated list of allowed header names."}}, "additionalProperties": false, "required": ["RequestHeadersAllowed"]}, {"type": "object", "description": "Adds headers with information about the original client request.", "properties": {"X-Forwarded": {"type": "string", "description": "Default action to apply to all X-Forwarded-* headers.", "enum": ["Set", "Append", "Remove", "Off"]}, "For": {"type": "string", "description": "Action to apply to the 'For' header.", "enum": ["Set", "Append", "Remove", "Off"]}, "Proto": {"type": "string", "description": "Action to apply to the 'Proto' header.", "enum": ["Set", "Append", "Remove", "Off"]}, "Host": {"type": "string", "description": "Action to apply to the 'Host' header.", "enum": ["Set", "Append", "Remove", "Off"]}, "Prefix": {"type": "string", "description": "Action to apply to the 'Prefix' header.", "enum": ["Set", "Append", "Remove", "Off"]}, "HeaderPrefix": {"type": "string", "description": "The header name prefix.", "default": "X-Forwarded-"}}, "additionalProperties": false, "required": ["X-Forwarded"]}, {"type": "object", "description": "Adds a header with information about the original client request.", "properties": {"Forwarded": {"type": "string", "pattern": "^(?:(?:for|by|proto|host),?)+$", "description": "A comma separated list containing any of these values: 'for,by,proto,host'."}, "ForFormat": {"type": "string", "description": "Format to apply to the 'For' header.", "enum": ["Random", "RandomAndPort", "RandomAndRandomPort", "Unknown", "UnknownAndPort", "UnknownAndRandomPort", "Ip", "IpAndPort", "IpAndRandomPort"]}, "ByFormat": {"type": "string", "description": "Format to apply to the 'For' header.", "enum": ["Random", "RandomAndPort", "RandomAndRandomPort", "Unknown", "UnknownAndPort", "UnknownAndRandomPort", "Ip", "IpAndPort", "IpAndRandomPort"]}, "Action": {"type": "string", "description": "Action to apply to the 'Forwarded' header.", "enum": ["Set", "Append", "Remove", "Off"]}}, "additionalProperties": false, "required": ["Forwarded"]}, {"type": "object", "description": "Forwards the client cert used on the inbound connection as a header to the destination.", "properties": {"ClientCert": {"type": "string", "description": "The header name to use for the forwarded client cert."}}, "additionalProperties": false, "required": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"type": "object", "description": "Transform controlling whether response headers are copied from the original response.", "properties": {"ResponseHeadersCopy": {"type": "boolean", "description": "If true, copies all response headers from the destination back to the client."}}, "additionalProperties": false, "required": ["ResponseHeadersCopy"]}, {"type": "object", "description": "Transform for setting, appending, or removing a response header.", "properties": {"ResponseHeader": {"type": "string", "description": "The header name to operate on."}, "Set": {"type": "string", "description": "Value to set the given response header to."}, "Append": {"type": "string", "description": "Value to append to the given response header."}, "When": {"type": "string", "enum": ["Success", "Always", "Failure"], "description": "Specifies if the response header should be included for all, successful, or failure responses. Any response with a status code less than 400 is considered a success."}}, "additionalProperties": false, "anyOf": [{"required": ["ResponseHeader", "Set"]}, {"required": ["ResponseHeader", "Append"]}]}, {"type": "object", "description": "Removes the specified header from the response.", "properties": {"ResponseHeaderRemove": {"type": "string", "description": "The header name."}, "When": {"type": "string", "enum": ["Success", "Always", "Failure"], "description": "Specifies if the response header should be included for all, successful, or failure responses. Any response with a status code less than 400 is considered a success."}}, "additionalProperties": false, "required": ["ResponseHeaderRemove"]}, {"type": "object", "description": "YARP copies most response headers to the proxy response by default, this transform disables ResponseHeadersCopy and only copies the given headers.", "properties": {"ResponseHeadersAllowed": {"type": "string", "pattern": "^[a-zA-Z0-9!#$%&'*+-.^_`|~;]+$", "description": "A semicolon separated list of allowed header names."}}, "additionalProperties": false, "required": ["ResponseHeadersAllowed"]}, {"type": "object", "description": "Transform controlling whether trailing response headers are copied from the original response.", "properties": {"ResponseTrailersCopy": {"type": "boolean", "description": "If true, copies all trailing response headers from the destination back to the client."}}, "additionalProperties": false, "required": ["ResponseTrailersCopy"]}, {"type": "object", "description": "Transform for setting, appending, or removing a response trailer.", "properties": {"ResponseTrailer": {"type": "string", "description": "The trailer name to operate on."}, "Set": {"type": "string", "description": "Value to set the given response trailer to."}, "Append": {"type": "string", "description": "Value to append to the given response trailer."}, "When": {"type": "string", "enum": ["Success", "Always", "Failure"], "description": "Specifies if the response trailer should be included for all, successful, or failure responses. Any response with a status code less than 400 is considered a success."}}, "additionalProperties": false, "anyOf": [{"required": ["ResponseTrailer", "Set"]}, {"required": ["ResponseTrailer", "Append"]}]}, {"type": "object", "description": "Removes the specified trailer from the response.", "properties": {"ResponseTrailerRemove": {"type": "string", "description": "The trailer name."}, "When": {"type": "string", "enum": ["Success", "Always", "Failure"], "description": "Specifies if the response trailer should be removed for all, successful, or failure responses. Any response with a status code less than 400 is considered a success."}}, "additionalProperties": false, "required": ["ResponseTrailerRemove"]}, {"type": "object", "description": "YARP copies most response trailers to the proxy response by default, this transform disables ResponseTrailersCopy and only copies the given headers.", "properties": {"ResponseTrailersAllowed": {"type": "string", "pattern": "^[a-zA-Z0-9!#$%&'*+-.^_`|~;]+$", "description": "A semicolon separated list of allowed trailer names."}}, "additionalProperties": false, "required": ["ResponseTrailersAllowed"]}]}}}, "required": ["ClusterId", "Match"], "additionalProperties": false}}}, "Clusters": {"type": "object", "description": "Named clusters describing destinations.", "patternProperties": {".": {"type": "object", "properties": {"Destinations": {"type": "object", "description": "Named destinations where traffic is forwarded.", "patternProperties": {".": {"type": "object", "properties": {"Address": {"type": "string", "description": "Destination address (must include scheme)."}, "Health": {"type": ["string", "null"], "description": "Optional override URL accepting active health check probes."}, "Host": {"type": ["string", "null"], "description": "Optional fallback host header value used if a host is not already specified by request transforms."}, "Metadata": {"type": ["object", "null"], "description": "Arbitrary key-value pairs for custom destination logic.", "additionalProperties": {"type": "string"}}}, "additionalProperties": false, "required": ["Address"]}}}, "LoadBalancingPolicy": {"anyOf": [{"type": "string", "enum": ["PowerOfTwoChoices", "FirstAlphabetical", "Random", "RoundR<PERSON>in", "LeastRequests"]}, {"type": ["string", "null"]}], "description": "Determines traffic distribution among destinations."}, "SessionAffinity": {"type": ["object", "null"], "description": "Session affinity is a mechanism to bind (affinitize) a causally related request sequence to the destination that handled the first request when the load is balanced among several destinations.", "properties": {"Enabled": {"type": ["boolean", "null"]}, "Policy": {"anyOf": [{"type": "string", "enum": ["HashCookie", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "CustomHeader"]}, {"type": ["string", "null"]}], "description": "Determines how the session will be stored and retrieved."}, "FailurePolicy": {"anyOf": [{"type": "string", "enum": ["Redistribute", "Return503Error"]}, {"type": ["string", "null"]}], "description": "Strategy for handling a missing destination for an affinitized request."}, "AffinityKeyName": {"type": "string", "description": "Identifies the name of the field where the affinity value is stored (cookie or header name)."}, "Cookie": {"type": ["object", "null"], "properties": {"Domain": {"type": ["string", "null"], "description": "Specifies the domain of the cookie."}, "Path": {"type": ["string", "null"], "description": "Specifies the path of the cookie."}, "Expiration": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Specifies the expiration of the cookie. Format: 'hh:mm:ss'."}, "MaxAge": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Specifies the maximum age of the cookie. Format: 'hh:mm:ss'."}, "SecurePolicy": {"type": ["string", "null"], "enum": ["Always", "None", "SameAsRequest"], "description": "Specifies the Secure attribute of the cookie."}, "HttpOnly": {"type": ["boolean", "null"], "description": "Specifies whether a cookie is accessible by client-side script."}, "SameSite": {"type": ["string", "null"], "enum": ["Lax", "None", "Strict", "Unspecified"], "description": "Specifies the SameSite attribute of the cookie."}, "IsEssential": {"type": ["boolean", "null"], "description": "Specifies whether a cookie is essential for the application to function correctly. If true then consent policy checks may be bypassed."}}, "additionalProperties": false}}, "additionalProperties": false, "required": ["AffinityKeyName"]}, "HealthCheck": {"type": ["object", "null"], "description": "Health check configuration for destinations.", "properties": {"Active": {"type": ["object", "null"], "description": "Active health checks are based on sending health probing requests.", "properties": {"Enabled": {"type": ["boolean", "null"], "description": "Determines if active health checks are enabled.", "default": false}, "Interval": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Period of sending health probing requests. Format: 'hh:mm:ss'.", "default": "00:00:15"}, "Timeout": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Period of waiting for a health check response. Format: 'hh:mm:ss'.", "default": "00:00:10"}, "Policy": {"anyOf": [{"type": "string", "enum": ["ConsecutiveFailures"]}, {"type": ["string", "null"]}], "description": "Determines the health check policy."}, "Path": {"type": ["string", "null"], "description": "HTTP health check endpoint path.", "default": "/"}, "Query": {"type": ["string", "null"], "description": "Query string to append to the probe, including the leading '?'."}}, "additionalProperties": false}, "Passive": {"type": ["object", "null"], "description": "Passive health checks are based on observing the health of the responses from the destination.", "properties": {"Enabled": {"type": ["boolean", "null"], "description": "Determines if passive health checks are enabled.", "default": false}, "Policy": {"anyOf": [{"type": "string", "enum": ["TransportFailureRate"]}, {"type": ["string", "null"]}], "description": "Determines the health check policy."}, "ReactivationPeriod": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Period after which an unhealthy destination reverts back to an Unknown health state. Format: 'hh:mm:ss'."}}, "additionalProperties": false}, "AvailableDestinationsPolicy": {"anyOf": [{"type": "string", "enum": ["HealthyAndUnknown", "HealthyOrPanic"]}, {"type": ["string", "null"]}]}}, "additionalProperties": false}, "HttpClient": {"type": ["object", "null"], "description": "Configuration for outbound HTTP connections.", "properties": {"SslProtocols": {"type": "array", "description": "Specifies the SSL protocols to use.", "items": {"type": "string"}}, "DangerousAcceptAnyServerCertificate": {"type": ["boolean", "null"], "description": "Determines whether the server's SSL certificate validity is checked by the client. Setting it to true completely disables validation.", "default": false}, "MaxConnectionsPerServer": {"type": ["number", "null"], "description": "Specifies the maximum number of connections per server."}, "EnableMultipleHttp2Connections": {"type": ["boolean", "null"], "description": "Determines if multiple HTTP/2 connections are enabled.", "default": true}, "RequestHeaderEncoding": {"type": ["string", "null"], "description": "Specifies the encoding of request headers, e.g. 'utf-8'."}, "ResponseHeaderEncoding": {"type": ["string", "null"], "description": "Specifies the encoding of response headers, e.g. 'utf-8'."}, "WebProxy": {"type": ["object", "null"], "description": "Config used to construct a System.Net.WebProxy instance used for outgoing requests.", "properties": {"Address": {"type": ["string", "null"], "description": "The URI of the proxy server."}, "BypassOnLocal": {"type": ["boolean", "null"], "description": "If true, bypasses the proxy for local addresses.", "default": false}, "UseDefaultCredentials": {"type": ["boolean", "null"], "description": "If true, sends CredentialCache.DefaultCredentials with requests.", "default": false}}, "additionalProperties": false}}, "additionalProperties": false}, "HttpRequest": {"type": ["object", "null"], "description": "Options controlling requests sent to destinations.", "properties": {"ActivityTimeout": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Specifies how long a request is allowed to remain idle between any operation completing, after which it will be canceled. Format: 'hh:mm:ss'.", "default": "00:01:40"}, "Version": {"type": ["string", "null"], "description": "Preferred version of the outgoing request.", "default": "2.0"}, "VersionPolicy": {"type": ["string", "null"], "description": "The policy applied to version selection, e.g. whether to prefer downgrades, upgrades or request an exact version.", "default": "RequestVersionOrLower", "enum": ["RequestVersionExact", "RequestVersionOrLower", "RequestVersion<PERSON>r<PERSON><PERSON><PERSON>"]}, "AllowResponseBuffering": {"type": ["boolean", "null"], "description": "Determines if response buffering is allowed."}}, "additionalProperties": false}, "Metadata": {"type": ["object", "null"], "description": "Arbitrary key-value pairs for custom cluster logic.", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}}}}}}, "title": "JSON schema ASP.NET Core's appsettings.json file", "type": "object"}