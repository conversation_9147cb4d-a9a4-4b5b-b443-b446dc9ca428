{"properties": {"ReverseProxy": {"type": "object", "description": "Reverse proxy configuration for routes and clusters.", "properties": {"Routes": {"type": "object", "description": "Named routes that direct incoming requests to clusters.", "patternProperties": {".": {"type": "object", "properties": {"ClusterId": {"type": "string", "description": "Name of the cluster this route points to."}, "Order": {"type": ["number", "null"], "description": "Order value for this route. Routes with lower numbers take precedence over higher numbers."}, "MaxRequestBodySize": {"type": ["number", "null"], "description": "An optional override for how large request bodies can be in bytes."}, "AuthorizationPolicy": {"type": ["string", "null"], "description": "Specifies which authorization policy applies, e.g. 'Default' or 'Anonymous'."}, "RateLimiterPolicy": {"type": ["string", "null"], "description": "The name of the RateLimiterPolicy to apply to this route."}, "OutputCachePolicy": {"type": ["string", "null"], "description": "The name of the OutputCachePolicy to apply to this route."}, "Timeout": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "The Timeout to apply to this route. This overrides any system defaults. Setting both Timeout and TimeoutPolicy is an error. Format: 'hh:mm:ss'."}, "TimeoutPolicy": {"type": ["string", "null"], "description": "Specifies which timeout policy applies, e.g. 'Default' or 'Disable'. Setting both Timeout and TimeoutPolicy is an error."}, "CorsPolicy": {"type": ["string", "null"], "description": "Specifies which CORS policy applies, e.g. 'Default' or 'Disable'."}, "Match": {"type": "object", "properties": {"Path": {"type": ["string", "null"], "description": "Path pattern using ASP.NET route template syntax, e.g. '/something/{**remainder}'."}, "Hosts": {"type": ["array", "null"], "description": "Only match requests with the given Host header. Supports wildcards and ports. For unicode host names, do not use punycode.", "items": {"type": "string"}}, "Methods": {"description": "Allowed HTTP methods.", "items": {"anyOf": [{"type": "string", "enum": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT", "TRACE"]}, {"type": ["array", "null"]}]}}, "Headers": {"type": ["array", "null"], "description": "List of header match conditions.", "items": {"type": "object", "properties": {"Name": {"type": "string", "description": "Header name."}, "Values": {"type": "array", "description": "Matches against any of these values.", "items": {"type": "string"}}, "Mode": {"type": "string", "description": "How the header values should be matched.", "enum": ["ExactHeader", "HeaderPrefix", "Contains", "NotContains", "Exists", "NotExists"]}, "IsCaseSensitive": {"type": "boolean"}}, "required": ["Name"], "additionalProperties": false}}, "QueryParameters": {"type": ["array", "null"], "description": "List of query string match conditions.", "items": {"type": "object", "properties": {"Name": {"type": "string", "description": "Name of the query parameter."}, "Values": {"type": "array", "description": "Matches against any of these values.", "items": {"type": "string"}}, "Mode": {"type": "string", "description": "How the query parameter values should be matched.", "enum": ["Exact", "Contains", "NotContains", "Prefix", "Exists"]}, "IsCaseSensitive": {"type": "boolean"}}, "required": ["Name"], "additionalProperties": false}}}, "additionalProperties": false, "anyOf": [{"required": ["Path"]}, {"required": ["Hosts"]}]}, "Metadata": {"type": ["object", "null"], "description": "Arbitrary key-value pairs for custom route logic.", "additionalProperties": {"type": "string"}}, "Transforms": {"type": ["array", "null"], "description": "List of transform objects for request customization.", "items": {"description": "A single transform definition.", "anyOf": [{"type": ["object", "null"], "$comment": "Fallback that matches any custom user-defined transforms.", "properties": {"RequestHeadersCopy": {"not": {}}, "RequestHeaderOriginalHost": {"not": {}}, "RequestHeader": {"not": {}}, "PathRemovePrefix": {"not": {}}, "PathSet": {"not": {}}, "PathPrefix": {"not": {}}, "QueryRouteParameter": {"not": {}}, "PathPattern": {"not": {}}, "QueryValueParameter": {"not": {}}, "QueryRemoveParameter": {"not": {}}, "HttpMethodChange": {"not": {}}, "RequestHeaderRouteValue": {"not": {}}, "RequestHeaderRemove": {"not": {}}, "RequestHeadersAllowed": {"not": {}}, "X-Forwarded": {"not": {}}, "Forwarded": {"not": {}}, "ClientCert": {"not": {}}, "ResponseHeadersCopy": {"not": {}}, "ResponseHeader": {"not": {}}, "ResponseHeaderRemove": {"not": {}}, "ResponseHeadersAllowed": {"not": {}}, "ResponseTrailersCopy": {"not": {}}, "ResponseTrailer": {"not": {}}, "ResponseTrailerRemove": {"not": {}}, "ResponseTrailersAllowed": {"not": {}}}}, {"type": "object", "description": "Sets whether incoming request headers are copied to the outbound request.", "properties": {"RequestHeadersCopy": {"type": "boolean", "description": "If true, copies all request headers to outbound request."}}, "additionalProperties": false, "required": ["RequestHeadersCopy"]}, {"type": "object", "description": "Specifies if the incoming request Host header should be copied to the proxy request.", "properties": {"RequestHeaderOriginalHost": {"type": "boolean", "description": "If true, preserve the original Host header; otherwise the destination's host is used."}}, "additionalProperties": false, "required": ["RequestHeaderOriginalHost"]}, {"type": "object", "description": "Transform for setting, appending, or removing a request header.", "properties": {"RequestHeader": {"type": "string", "description": "The header name to operate on."}, "Set": {"type": "string", "description": "Value to set the given request header to."}, "Append": {"type": "string", "description": "Value to append to the given request header."}, "Remove": {"type": "boolean", "description": "Removes the header if true."}}, "additionalProperties": false, "anyOf": [{"required": ["RequestHeader", "Set"]}, {"required": ["RequestHeader", "Append"]}, {"required": ["RequestHeader", "Remove"]}]}, {"type": "object", "description": "Transform that removes a specified prefix from the request path.", "properties": {"PathRemovePrefix": {"type": "string", "description": "Prefix to remove from the existing request path."}}, "additionalProperties": false, "required": ["PathRemovePrefix"]}, {"type": "object", "description": "Transform that replaces the entire request path with the provided value.", "properties": {"PathSet": {"type": "string", "description": "Sets the request path to this value."}}, "additionalProperties": false, "required": ["PathSet"]}, {"type": "object", "description": "Transform that adds the specified prefix to the request path.", "properties": {"PathPrefix": {"type": "string", "description": "Path prefix to add."}}, "additionalProperties": false, "required": ["PathPrefix"]}, {"type": "object", "description": "Transform that adds or replaces a query string parameter with a value from the route configuration.", "properties": {"QueryRouteParameter": {"type": "string", "description": "Specifies the query parameter name to add or replace."}, "Set": {"type": "string", "description": "Name of the route paramter to set the query parameter to."}, "Append": {"type": "string", "description": "Name of the route paramter to append to the query parameter."}}, "additionalProperties": false, "anyOf": [{"required": ["QueryRouteParameter", "Set"]}, {"required": ["QueryRouteParameter", "Append"]}]}, {"type": "object", "description": "Transform that replaces the entire request path using a pattern template, replacing {} segments with the route value.", "properties": {"PathPattern": {"type": "string", "description": "A path template starting with a '/', e.g. '/my/{plugin}/api/{**remainder}'."}}, "additionalProperties": false, "required": ["PathPattern"]}, {"type": "object", "description": "Adds or replaces parameters in the request query string.", "properties": {"QueryValueParameter": {"type": "string", "description": "Name of a query string parameter."}, "Set": {"type": "string", "description": "Value to set the given query parameter to."}, "Append": {"type": "string", "description": "Value to append to the given query parameter."}}, "additionalProperties": false, "anyOf": [{"required": ["QueryValueParameter", "Set"]}, {"required": ["QueryValueParameter", "Append"]}]}, {"type": "object", "description": "Removes the specified parameter from the request query string.", "properties": {"QueryRemoveParameter": {"type": "string", "description": "Name of a query string parameter."}}, "additionalProperties": false, "required": ["QueryRemoveParameter"]}, {"type": "object", "description": "Changes the http method used in the request.", "properties": {"HttpMethodChange": {"description": "The HTTP method to replace.", "anyOf": [{"type": "string", "enum": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT", "TRACE"]}, {"type": "string"}]}, "Set": {"type": "string", "description": "The new HTTP method."}}, "additionalProperties": false, "required": ["HttpMethodChange", "Set"]}, {"type": "object", "description": "Adds or replaces a header with a value from the route configuration.", "properties": {"RequestHeaderRouteValue": {"type": "string", "description": "The header name to operate on."}, "Set": {"type": "string", "description": "Route value to set the given header to."}, "Append": {"type": "string", "description": "Route value to append to the given header."}}, "additionalProperties": false, "anyOf": [{"required": ["RequestHeaderRouteValue", "Set"]}, {"required": ["RequestHeaderRouteValue", "Append"]}]}, {"type": "object", "description": "Removes the specified header from the request.", "properties": {"RequestHeaderRemove": {"type": "string", "description": "The header name."}}, "additionalProperties": false, "required": ["RequestHeaderRemove"]}, {"type": "object", "description": "YARP copies most request headers to the proxy request by default, this transform disables RequestHeadersCopy and only copies the given headers.", "properties": {"RequestHeadersAllowed": {"type": "string", "pattern": "^[a-zA-Z0-9!#$%&'*+-.^_`|~;]+$", "description": "A semicolon separated list of allowed header names."}}, "additionalProperties": false, "required": ["RequestHeadersAllowed"]}, {"type": "object", "description": "Adds headers with information about the original client request.", "properties": {"X-Forwarded": {"type": "string", "description": "Default action to apply to all X-Forwarded-* headers.", "enum": ["Set", "Append", "Remove", "Off"]}, "For": {"type": "string", "description": "Action to apply to the 'For' header.", "enum": ["Set", "Append", "Remove", "Off"]}, "Proto": {"type": "string", "description": "Action to apply to the 'Proto' header.", "enum": ["Set", "Append", "Remove", "Off"]}, "Host": {"type": "string", "description": "Action to apply to the 'Host' header.", "enum": ["Set", "Append", "Remove", "Off"]}, "Prefix": {"type": "string", "description": "Action to apply to the 'Prefix' header.", "enum": ["Set", "Append", "Remove", "Off"]}, "HeaderPrefix": {"type": "string", "description": "The header name prefix.", "default": "X-Forwarded-"}}, "additionalProperties": false, "required": ["X-Forwarded"]}, {"type": "object", "description": "Adds a header with information about the original client request.", "properties": {"Forwarded": {"type": "string", "pattern": "^(?:(?:for|by|proto|host),?)+$", "description": "A comma separated list containing any of these values: 'for,by,proto,host'."}, "ForFormat": {"type": "string", "description": "Format to apply to the 'For' header.", "enum": ["Random", "RandomAndPort", "RandomAndRandomPort", "Unknown", "UnknownAndPort", "UnknownAndRandomPort", "Ip", "IpAndPort", "IpAndRandomPort"]}, "ByFormat": {"type": "string", "description": "Format to apply to the 'For' header.", "enum": ["Random", "RandomAndPort", "RandomAndRandomPort", "Unknown", "UnknownAndPort", "UnknownAndRandomPort", "Ip", "IpAndPort", "IpAndRandomPort"]}, "Action": {"type": "string", "description": "Action to apply to the 'Forwarded' header.", "enum": ["Set", "Append", "Remove", "Off"]}}, "additionalProperties": false, "required": ["Forwarded"]}, {"type": "object", "description": "Forwards the client cert used on the inbound connection as a header to the destination.", "properties": {"ClientCert": {"type": "string", "description": "The header name to use for the forwarded client cert."}}, "additionalProperties": false, "required": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"type": "object", "description": "Transform controlling whether response headers are copied from the original response.", "properties": {"ResponseHeadersCopy": {"type": "boolean", "description": "If true, copies all response headers from the destination back to the client."}}, "additionalProperties": false, "required": ["ResponseHeadersCopy"]}, {"type": "object", "description": "Transform for setting, appending, or removing a response header.", "properties": {"ResponseHeader": {"type": "string", "description": "The header name to operate on."}, "Set": {"type": "string", "description": "Value to set the given response header to."}, "Append": {"type": "string", "description": "Value to append to the given response header."}, "When": {"type": "string", "enum": ["Success", "Always", "Failure"], "description": "Specifies if the response header should be included for all, successful, or failure responses. Any response with a status code less than 400 is considered a success."}}, "additionalProperties": false, "anyOf": [{"required": ["ResponseHeader", "Set"]}, {"required": ["ResponseHeader", "Append"]}]}, {"type": "object", "description": "Removes the specified header from the response.", "properties": {"ResponseHeaderRemove": {"type": "string", "description": "The header name."}, "When": {"type": "string", "enum": ["Success", "Always", "Failure"], "description": "Specifies if the response header should be included for all, successful, or failure responses. Any response with a status code less than 400 is considered a success."}}, "additionalProperties": false, "required": ["ResponseHeaderRemove"]}, {"type": "object", "description": "YARP copies most response headers to the proxy response by default, this transform disables ResponseHeadersCopy and only copies the given headers.", "properties": {"ResponseHeadersAllowed": {"type": "string", "pattern": "^[a-zA-Z0-9!#$%&'*+-.^_`|~;]+$", "description": "A semicolon separated list of allowed header names."}}, "additionalProperties": false, "required": ["ResponseHeadersAllowed"]}, {"type": "object", "description": "Transform controlling whether trailing response headers are copied from the original response.", "properties": {"ResponseTrailersCopy": {"type": "boolean", "description": "If true, copies all trailing response headers from the destination back to the client."}}, "additionalProperties": false, "required": ["ResponseTrailersCopy"]}, {"type": "object", "description": "Transform for setting, appending, or removing a response trailer.", "properties": {"ResponseTrailer": {"type": "string", "description": "The trailer name to operate on."}, "Set": {"type": "string", "description": "Value to set the given response trailer to."}, "Append": {"type": "string", "description": "Value to append to the given response trailer."}, "When": {"type": "string", "enum": ["Success", "Always", "Failure"], "description": "Specifies if the response trailer should be included for all, successful, or failure responses. Any response with a status code less than 400 is considered a success."}}, "additionalProperties": false, "anyOf": [{"required": ["ResponseTrailer", "Set"]}, {"required": ["ResponseTrailer", "Append"]}]}, {"type": "object", "description": "Removes the specified trailer from the response.", "properties": {"ResponseTrailerRemove": {"type": "string", "description": "The trailer name."}, "When": {"type": "string", "enum": ["Success", "Always", "Failure"], "description": "Specifies if the response trailer should be removed for all, successful, or failure responses. Any response with a status code less than 400 is considered a success."}}, "additionalProperties": false, "required": ["ResponseTrailerRemove"]}, {"type": "object", "description": "YARP copies most response trailers to the proxy response by default, this transform disables ResponseTrailersCopy and only copies the given headers.", "properties": {"ResponseTrailersAllowed": {"type": "string", "pattern": "^[a-zA-Z0-9!#$%&'*+-.^_`|~;]+$", "description": "A semicolon separated list of allowed trailer names."}}, "additionalProperties": false, "required": ["ResponseTrailersAllowed"]}]}}}, "required": ["ClusterId", "Match"], "additionalProperties": false}}}, "Clusters": {"type": "object", "description": "Named clusters describing destinations.", "patternProperties": {".": {"type": "object", "properties": {"Destinations": {"type": "object", "description": "Named destinations where traffic is forwarded.", "patternProperties": {".": {"type": "object", "properties": {"Address": {"type": "string", "description": "Destination address (must include scheme)."}, "Health": {"type": ["string", "null"], "description": "Optional override URL accepting active health check probes."}, "Host": {"type": ["string", "null"], "description": "Optional fallback host header value used if a host is not already specified by request transforms."}, "Metadata": {"type": ["object", "null"], "description": "Arbitrary key-value pairs for custom destination logic.", "additionalProperties": {"type": "string"}}}, "additionalProperties": false, "required": ["Address"]}}}, "LoadBalancingPolicy": {"anyOf": [{"type": "string", "enum": ["PowerOfTwoChoices", "FirstAlphabetical", "Random", "RoundR<PERSON>in", "LeastRequests"]}, {"type": ["string", "null"]}], "description": "Determines traffic distribution among destinations."}, "SessionAffinity": {"type": ["object", "null"], "description": "Session affinity is a mechanism to bind (affinitize) a causally related request sequence to the destination that handled the first request when the load is balanced among several destinations.", "properties": {"Enabled": {"type": ["boolean", "null"]}, "Policy": {"anyOf": [{"type": "string", "enum": ["HashCookie", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "CustomHeader"]}, {"type": ["string", "null"]}], "description": "Determines how the session will be stored and retrieved."}, "FailurePolicy": {"anyOf": [{"type": "string", "enum": ["Redistribute", "Return503Error"]}, {"type": ["string", "null"]}], "description": "Strategy for handling a missing destination for an affinitized request."}, "AffinityKeyName": {"type": "string", "description": "Identifies the name of the field where the affinity value is stored (cookie or header name)."}, "Cookie": {"type": ["object", "null"], "properties": {"Domain": {"type": ["string", "null"], "description": "Specifies the domain of the cookie."}, "Path": {"type": ["string", "null"], "description": "Specifies the path of the cookie."}, "Expiration": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Specifies the expiration of the cookie. Format: 'hh:mm:ss'."}, "MaxAge": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Specifies the maximum age of the cookie. Format: 'hh:mm:ss'."}, "SecurePolicy": {"type": ["string", "null"], "enum": ["Always", "None", "SameAsRequest"], "description": "Specifies the Secure attribute of the cookie."}, "HttpOnly": {"type": ["boolean", "null"], "description": "Specifies whether a cookie is accessible by client-side script."}, "SameSite": {"type": ["string", "null"], "enum": ["Lax", "None", "Strict", "Unspecified"], "description": "Specifies the SameSite attribute of the cookie."}, "IsEssential": {"type": ["boolean", "null"], "description": "Specifies whether a cookie is essential for the application to function correctly. If true then consent policy checks may be bypassed."}}, "additionalProperties": false}}, "additionalProperties": false, "required": ["AffinityKeyName"]}, "HealthCheck": {"type": ["object", "null"], "description": "Health check configuration for destinations.", "properties": {"Active": {"type": ["object", "null"], "description": "Active health checks are based on sending health probing requests.", "properties": {"Enabled": {"type": ["boolean", "null"], "description": "Determines if active health checks are enabled.", "default": false}, "Interval": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Period of sending health probing requests. Format: 'hh:mm:ss'.", "default": "00:00:15"}, "Timeout": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Period of waiting for a health check response. Format: 'hh:mm:ss'.", "default": "00:00:10"}, "Policy": {"anyOf": [{"type": "string", "enum": ["ConsecutiveFailures"]}, {"type": ["string", "null"]}], "description": "Determines the health check policy."}, "Path": {"type": ["string", "null"], "description": "HTTP health check endpoint path.", "default": "/"}, "Query": {"type": ["string", "null"], "description": "Query string to append to the probe, including the leading '?'."}}, "additionalProperties": false}, "Passive": {"type": ["object", "null"], "description": "Passive health checks are based on observing the health of the responses from the destination.", "properties": {"Enabled": {"type": ["boolean", "null"], "description": "Determines if passive health checks are enabled.", "default": false}, "Policy": {"anyOf": [{"type": "string", "enum": ["TransportFailureRate"]}, {"type": ["string", "null"]}], "description": "Determines the health check policy."}, "ReactivationPeriod": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Period after which an unhealthy destination reverts back to an Unknown health state. Format: 'hh:mm:ss'."}}, "additionalProperties": false}, "AvailableDestinationsPolicy": {"anyOf": [{"type": "string", "enum": ["HealthyAndUnknown", "HealthyOrPanic"]}, {"type": ["string", "null"]}]}}, "additionalProperties": false}, "HttpClient": {"type": ["object", "null"], "description": "Configuration for outbound HTTP connections.", "properties": {"SslProtocols": {"type": "array", "description": "Specifies the SSL protocols to use.", "items": {"type": "string"}}, "DangerousAcceptAnyServerCertificate": {"type": ["boolean", "null"], "description": "Determines whether the server's SSL certificate validity is checked by the client. Setting it to true completely disables validation.", "default": false}, "MaxConnectionsPerServer": {"type": ["number", "null"], "description": "Specifies the maximum number of connections per server."}, "EnableMultipleHttp2Connections": {"type": ["boolean", "null"], "description": "Determines if multiple HTTP/2 connections are enabled.", "default": true}, "RequestHeaderEncoding": {"type": ["string", "null"], "description": "Specifies the encoding of request headers, e.g. 'utf-8'."}, "ResponseHeaderEncoding": {"type": ["string", "null"], "description": "Specifies the encoding of response headers, e.g. 'utf-8'."}, "WebProxy": {"type": ["object", "null"], "description": "Config used to construct a System.Net.WebProxy instance used for outgoing requests.", "properties": {"Address": {"type": ["string", "null"], "description": "The URI of the proxy server."}, "BypassOnLocal": {"type": ["boolean", "null"], "description": "If true, bypasses the proxy for local addresses.", "default": false}, "UseDefaultCredentials": {"type": ["boolean", "null"], "description": "If true, sends CredentialCache.DefaultCredentials with requests.", "default": false}}, "additionalProperties": false}}, "additionalProperties": false}, "HttpRequest": {"type": ["object", "null"], "description": "Options controlling requests sent to destinations.", "properties": {"ActivityTimeout": {"type": ["string", "null"], "pattern": "^\\d*?\\.?\\d\\d:\\d\\d:\\d\\d\\.?\\d{0,7}$", "description": "Specifies how long a request is allowed to remain idle between any operation completing, after which it will be canceled. Format: 'hh:mm:ss'.", "default": "00:01:40"}, "Version": {"type": ["string", "null"], "description": "Preferred version of the outgoing request.", "default": "2.0"}, "VersionPolicy": {"type": ["string", "null"], "description": "The policy applied to version selection, e.g. whether to prefer downgrades, upgrades or request an exact version.", "default": "RequestVersionOrLower", "enum": ["RequestVersionExact", "RequestVersionOrLower", "RequestVersion<PERSON>r<PERSON><PERSON><PERSON>"]}, "AllowResponseBuffering": {"type": ["boolean", "null"], "description": "Determines if response buffering is allowed."}}, "additionalProperties": false}, "Metadata": {"type": ["object", "null"], "description": "Arbitrary key-value pairs for custom cluster logic.", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}}}}}}, "type": "object", "SourceSegments": "C:\\Users\\<USER>\\.nuget\\packages\\yarp.reverseproxy\\2.3.0\\ConfigurationSchema.json"}